import type { TextEditor, Uri } from 'vscode';
import { ProgressLocation, window } from 'vscode';
import type { Sources } from '../constants.telemetry';
import type { Container } from '../container';
import { GitUri } from '../git/gitUri';
import type { GitReference } from '../git/models/reference';
import { createReference } from '../git/utils/reference.utils';
import { showGenericErrorMessage } from '../messages';
import { getBestRepositoryOrShowPicker } from '../quickpicks/repositoryPicker';
import { command } from '../system/-webview/command';
import { Logger } from '../system/logger';
import { ActiveEditorCommand } from './commandBase';
import { getCommandUri } from './commandBase.utils';
import type { CommandContext } from './commandContext';

export interface InteractiveCommitComposerCommandArgs {
	repoPath?: string | Uri;
	source?: Sources;
	sourceType?: 'workingChanges' | 'commitRange';
	sourceRef?: string;
	targetRef?: string;
}

@command()
export class InteractiveCommitComposerCommand extends ActiveEditorCommand {
	constructor(private readonly container: Container) {
		super([
			'gitlens.commitComposer.interactive',
			'gitlens.commitComposer.fromWorkingChanges',
			'gitlens.commitComposer.fromCommitRange',
		]);
	}

	protected override preExecute(context: CommandContext, args?: InteractiveCommitComposerCommandArgs): Promise<void> {
		let sourceType: 'workingChanges' | 'commitRange' = 'workingChanges';
		let source: Sources | undefined = args?.source;

		// Determine source type from command
		if (context.command === 'gitlens.commitComposer.fromWorkingChanges') {
			sourceType = 'workingChanges';
			source = source ?? 'commandPalette';
		} else if (context.command === 'gitlens.commitComposer.fromCommitRange') {
			sourceType = 'commitRange';
			source = source ?? 'commandPalette';
		}

		// Handle SCM context
		if (context.type === 'scm' && context.scm.rootUri != null) {
			args = { ...args, repoPath: context.scm.rootUri };
			sourceType = 'workingChanges';
			source = 'scm-input';
		}

		return this.execute(context.editor, context.uri, {
			...args,
			sourceType: sourceType,
			source: source,
		});
	}

	async execute(editor?: TextEditor, uri?: Uri, args?: InteractiveCommitComposerCommandArgs): Promise<void> {
		const scope = Logger.getNewLogScope('InteractiveCommitComposerCommand');

		try {
			// Get repository
			uri = getCommandUri(uri, editor);
			const gitUri = uri != null ? await GitUri.fromUri(uri) : undefined;

			const repository = await getBestRepositoryOrShowPicker(gitUri, editor, 'Interactive Commit Composer');

			if (repository == null) {
				const result = await window.showWarningMessage(
					'Unable to find a valid repository. Please open a folder with a Git repository.',
					'Open Folder',
				);
				if (result === 'Open Folder') {
					await window.showOpenDialog({
						canSelectFolders: true,
						canSelectFiles: false,
						canSelectMany: false,
						openLabel: 'Open Repository',
					});
				}
				return;
			}

			const sourceType = args?.sourceType ?? 'workingChanges';

			// Validate source type requirements
			if (sourceType === 'commitRange') {
				if (!args?.sourceRef || !args?.targetRef) {
					void window.showErrorMessage(
						'Commit range composition requires both source and target references.',
					);
					return;
				}
			} else if (sourceType === 'workingChanges') {
				// Check if there are working changes
				const status = await repository.getStatus();
				if (!status?.hasChanges) {
					void window.showInformationMessage(
						'No working changes found. Make some changes to your files and try again.',
					);
					return;
				}
			}

			// Show progress while initializing
			await window.withProgress(
				{
					location: ProgressLocation.Notification,
					title: 'Initializing Interactive Commit Composer...',
					cancellable: true,
				},
				async (progress, cancellation) => {
					try {
						progress.report({ message: 'Analyzing changes...' });

						// Create composer session
						const sourceRef = args?.sourceRef
							? createReference(args.sourceRef, repository.path, { refType: 'revision' })
							: undefined;
						const targetRef = args?.targetRef
							? createReference(args.targetRef, repository.path, { refType: 'revision' })
							: undefined;

						const session = await this.container.commitComposer.createSession(repository, sourceType, {
							sourceRef: sourceRef,
							targetRef: targetRef,
							cancellation: cancellation,
						});

						if (cancellation.isCancellationRequested) return;

						progress.report({ message: 'Opening interactive interface...' });

						// Show the commit composer webview
						await this.container.views.commitComposer.show(
							{ preserveFocus: false },
							{
								sessionId: session.id,
								sourceType: sourceType,
								sourceRef: args?.sourceRef,
								targetRef: args?.targetRef,
							},
						);
					} catch (ex) {
						Logger.error(ex, scope, 'Failed to initialize commit composer');

						if (ex instanceof Error && ex.message.includes('No changes')) {
							void window.showInformationMessage('No changes found to compose commits from.');
							return;
						}

						void showGenericErrorMessage('Failed to initialize Interactive Commit Composer');
					}
				},
			);
		} catch (ex) {
			Logger.error(ex, scope);
			void showGenericErrorMessage('Failed to start Interactive Commit Composer');
		}
	}
}

// Helper command for generating commits from working changes (existing functionality integration)
@command()
export class GenerateCommitsFromWorkingChangesCommand extends ActiveEditorCommand {
	constructor(private readonly container: Container) {
		super(['gitlens.commitComposer.generateFromWorkingChanges']);
	}

	async execute(editor?: TextEditor, uri?: Uri): Promise<void> {
		// This integrates with the existing generate commits functionality
		// but opens the interactive composer instead of directly creating commits
		const composerCommand = new InteractiveCommitComposerCommand(this.container);
		await composerCommand.execute(editor, uri, {
			sourceType: 'workingChanges',
			source: 'commandPalette',
		});
	}
}

// Helper command for generating commits from commit range
@command()
export class GenerateCommitsFromRangeCommand extends ActiveEditorCommand {
	constructor(private readonly container: Container) {
		super(['gitlens.commitComposer.generateFromRange']);
	}

	async execute(editor?: TextEditor, uri?: Uri, args?: { sourceRef?: string; targetRef?: string }): Promise<void> {
		if (!args?.sourceRef || !args?.targetRef) {
			// Show quick pick to select commit range
			void window.showInformationMessage(
				'Please select a commit range using the Git graph or commit history views.',
			);
			return;
		}

		const composerCommand = new InteractiveCommitComposerCommand(this.container);
		await composerCommand.execute(editor, uri, {
			sourceType: 'commitRange',
			sourceRef: args.sourceRef,
			targetRef: args.targetRef,
			source: 'commandPalette',
		});
	}
}
