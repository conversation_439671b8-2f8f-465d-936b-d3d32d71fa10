import type { GitFileChange } from '../../git/models/file';

export interface DraftCommit {
	readonly id: string;
	readonly index: number;
	readonly message: string;
	readonly description?: string;
	readonly files: GitFileChange[];
	readonly hunks: DraftHunk[];
	readonly author?: {
		name: string;
		email: string;
	};
	readonly timestamp: number;
	readonly isGenerated: boolean;
	readonly parentCommitId?: string;
	readonly branchId?: string;
}

export interface DraftHunk {
	readonly id: string;
	readonly fileUri: string;
	readonly oldStart: number;
	readonly oldLines: number;
	readonly newStart: number;
	readonly newLines: number;
	readonly content: string;
	readonly header: string;
	readonly isSelected: boolean;
	readonly commitId?: string;
}

export interface DraftCommitChange {
	readonly type: 'create' | 'update' | 'delete' | 'reorder' | 'move';
	readonly commitId: string;
	readonly data?: Partial<DraftCommit>;
	readonly newIndex?: number;
	readonly targetBranchId?: string;
}

export interface CommitValidationResult {
	readonly isValid: boolean;
	readonly errors: string[];
	readonly warnings: string[];
}

export function createDraftCommit(
	id: string,
	index: number,
	message: string,
	files: GitFileChange[] = [],
	hunks: DraftHunk[] = [],
	options?: {
		description?: string;
		author?: { name: string; email: string };
		parentCommitId?: string;
		branchId?: string;
		isGenerated?: boolean;
	},
): DraftCommit {
	return {
		id: id,
		index: index,
		message: message,
		description: options?.description,
		files: files,
		hunks: hunks,
		author: options?.author,
		timestamp: Date.now(),
		isGenerated: options?.isGenerated ?? true,
		parentCommitId: options?.parentCommitId,
		branchId: options?.branchId,
	};
}

export function createDraftHunk(
	id: string,
	fileUri: string,
	oldStart: number,
	oldLines: number,
	newStart: number,
	newLines: number,
	content: string,
	header: string,
	options?: {
		isSelected?: boolean;
		commitId?: string;
	},
): DraftHunk {
	return {
		id: id,
		fileUri: fileUri,
		oldStart: oldStart,
		oldLines: oldLines,
		newStart: newStart,
		newLines: newLines,
		content: content,
		header: header,
		isSelected: options?.isSelected ?? false,
		commitId: options?.commitId,
	};
}

export function validateDraftCommit(commit: DraftCommit): CommitValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Validate commit message
	if (!commit.message.trim()) {
		errors.push('Commit message cannot be empty');
	} else if (commit.message.length > 72) {
		warnings.push('Commit message is longer than 72 characters');
	}

	// Validate that commit has changes
	if (commit.files.length === 0 && commit.hunks.length === 0) {
		errors.push('Commit must contain at least one file change');
	}

	// Validate hunks belong to files
	const fileUris = new Set(commit.files.map(f => f.uri.toString()));
	for (const hunk of commit.hunks) {
		if (!fileUris.has(hunk.fileUri)) {
			errors.push(`Hunk references file not included in commit: ${hunk.fileUri}`);
		}
	}

	return {
		isValid: errors.length === 0,
		errors: errors,
		warnings: warnings,
	};
}

export function updateDraftCommit(commit: DraftCommit, changes: Partial<DraftCommit>): DraftCommit {
	return {
		...commit,
		...changes,
		timestamp: Date.now(),
	};
}

export function moveDraftHunk(hunk: DraftHunk, targetCommitId: string): DraftHunk {
	return {
		...hunk,
		commitId: targetCommitId,
	};
}
