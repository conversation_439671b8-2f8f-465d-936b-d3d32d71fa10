import type { <PERSON><PERSON> } from 'vscode';
import { ProgressLocation, window } from 'vscode';
import type { Source } from '../constants.telemetry';
import type { Container } from '../container';
import type { GitReference } from '../git/models/reference';
import type { Repository } from '../git/models/repository';
import { showGenericErrorMessage } from '../messages';
import { getRepositoryOrShowPicker } from '../quickpicks/repositoryPicker';
import { command } from '../system/-webview/command';
import { Logger } from '../system/logger';
import { GlCommandBase } from './commandBase';
import type { CommandContext } from './commandContext';

export interface StartCommitComposerCommandArgs {
	readonly repoPath?: string;
	readonly source?: Source;
	readonly sourceType?: 'workingChanges' | 'commitRange';
	readonly sourceRef?: GitReference;
	readonly targetRef?: GitReference;
}

export interface OpenCommitComposerCommandArgs {
	readonly sessionId: string;
	readonly source?: Source;
}

@command()
export class StartCommitComposerCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.start');
	}

	async execute(args?: StartCommitComposerCommandArgs): Promise<void> {
		try {
			let repository: Repository;
			if (args?.repoPath) {
				const repo = this.container.git.getRepository(args.repoPath);
				if (!repo) {
					void showGenericErrorMessage('Repository not found');
					return;
				}
				repository = repo;
			} else {
				const repo = await getRepositoryOrShowPicker('Start Commit Composer');
				if (!repo) return;
				repository = repo;
			}

			// Show progress while creating the session
			const session = await window.withProgress(
				{
					location: ProgressLocation.Notification,
					title: 'Starting Commit Composer...',
					cancellable: true,
				},
				async (progress, cancellation) => {
					progress.report({ message: 'Analyzing changes...' });

					return this.container.commitComposer.createSession(
						repository,
						args?.sourceType ?? 'workingChanges',
						{
							sourceRef: args?.sourceRef,
							targetRef: args?.targetRef,
							cancellation,
						},
					);
				},
			);

			if (!session) return;

			// Open the commit composer webview
			await this.container.webviews.showCommitComposer(session.id);

			// Log telemetry
			this.container.telemetry.sendEvent('commitComposer/started', {
				'source.type': args?.sourceType ?? 'workingChanges',
				'commits.count': session.commits.length,
				'branches.count': session.branches.length,
				'hunks.count': session.unassignedHunks.length,
			});
		} catch (ex) {
			Logger.error(ex, 'StartCommitComposerCommand', 'execute');
			void showGenericErrorMessage('Unable to start Commit Composer');
		}
	}
}

@command()
export class StartCommitComposerFromWorkingChangesCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.startFromWorkingChanges');
	}

	async execute(args?: { repoPath?: string; source?: Source }): Promise<void> {
		await this.container.commands.execute('gitlens.commitComposer.start', {
			...args,
			sourceType: 'workingChanges',
		} satisfies StartCommitComposerCommandArgs);
	}
}

@command()
export class StartCommitComposerFromCommitRangeCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.startFromCommitRange');
	}

	async execute(args?: {
		repoPath?: string;
		sourceRef?: GitReference;
		targetRef?: GitReference;
		source?: Source;
	}): Promise<void> {
		await this.container.commands.execute('gitlens.commitComposer.start', {
			...args,
			sourceType: 'commitRange',
		} satisfies StartCommitComposerCommandArgs);
	}
}

@command()
export class OpenCommitComposerCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.open');
	}

	async execute(args?: OpenCommitComposerCommandArgs): Promise<void> {
		try {
			if (!args?.sessionId) {
				void showGenericErrorMessage('No session ID provided');
				return;
			}

			const session = this.container.commitComposer.getSession(args.sessionId);
			if (!session) {
				void showGenericErrorMessage('Commit Composer session not found');
				return;
			}

			await this.container.webviews.showCommitComposer(args.sessionId);
		} catch (ex) {
			Logger.error(ex, 'OpenCommitComposerCommand', 'execute');
			void showGenericErrorMessage('Unable to open Commit Composer');
		}
	}
}

@command()
export class ExecuteCommitCompositionCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.execute');
	}

	async execute(args?: { sessionId: string; source?: Source }): Promise<void> {
		try {
			if (!args?.sessionId) {
				void showGenericErrorMessage('No session ID provided');
				return;
			}

			const session = this.container.commitComposer.getSession(args.sessionId);
			if (!session) {
				void showGenericErrorMessage('Commit Composer session not found');
				return;
			}

			// Confirm execution with user
			const result = await window.showWarningMessage(
				`Are you sure you want to execute this commit composition? This will create ${session.commits.length} commit(s) and ${session.branches.length} branch(es).`,
				{ modal: true },
				'Execute',
				'Cancel',
			);

			if (result !== 'Execute') return;

			// Execute with progress
			const executionResult = await window.withProgress(
				{
					location: ProgressLocation.Notification,
					title: 'Executing Commit Composition...',
					cancellable: false,
				},
				async progress => {
					progress.report({ message: 'Creating commits and branches...' });
					return this.container.commitComposer.executeComposition(args.sessionId);
				},
			);

			if (executionResult.success) {
				void window.showInformationMessage(
					`Commit composition executed successfully! Created ${executionResult.createdCommits.length} commit(s) and ${executionResult.createdBranches.length} branch(es).`,
				);

				// Close the session
				this.container.commitComposer.closeSession(args.sessionId);

				// Log telemetry
				this.container.telemetry.sendEvent('commitComposer/executed', {
					'commits.created': executionResult.createdCommits.length,
					'branches.created': executionResult.createdBranches.length,
					success: true,
				});
			} else {
				void window.showErrorMessage(
					`Failed to execute commit composition: ${executionResult.errors.join(', ')}`,
				);

				// Log telemetry
				this.container.telemetry.sendEvent('commitComposer/executed', {
					'commits.created': 0,
					'branches.created': 0,
					success: false,
					'error.count': executionResult.errors.length,
				});
			}
		} catch (ex) {
			Logger.error(ex, 'ExecuteCommitCompositionCommand', 'execute');
			void showGenericErrorMessage('Unable to execute commit composition');
		}
	}
}

@command()
export class CancelCommitCompositionCommand extends GlCommandBase {
	constructor(private readonly container: Container) {
		super('gitlens.commitComposer.cancel');
	}

	async execute(args?: { sessionId: string; source?: Source }): Promise<void> {
		try {
			if (!args?.sessionId) {
				void showGenericErrorMessage('No session ID provided');
				return;
			}

			const session = this.container.commitComposer.getSession(args.sessionId);
			if (!session) {
				void showGenericErrorMessage('Commit Composer session not found');
				return;
			}

			// Confirm cancellation if there are unsaved changes
			if (session.hasUnsavedChanges) {
				const result = await window.showWarningMessage(
					'You have unsaved changes in the Commit Composer. Are you sure you want to cancel?',
					{ modal: true },
					'Cancel Composition',
					'Keep Working',
				);

				if (result !== 'Cancel Composition') return;
			}

			// Close the session
			this.container.commitComposer.closeSession(args.sessionId);

			void window.showInformationMessage('Commit composition cancelled');

			// Log telemetry
			this.container.telemetry.sendEvent('commitComposer/cancelled', {
				'commits.count': session.commits.length,
				'branches.count': session.branches.length,
				'had.unsaved.changes': session.hasUnsavedChanges,
			});
		} catch (ex) {
			Logger.error(ex, 'CancelCommitCompositionCommand', 'execute');
			void showGenericErrorMessage('Unable to cancel commit composition');
		}
	}
}
