export interface DraftBranch {
	readonly id: string;
	readonly name: string;
	readonly baseBranch: string;
	readonly baseCommit: string;
	readonly commitIds: string[];
	readonly isDefault: boolean;
	readonly color?: string;
	readonly description?: string;
	readonly timestamp: number;
}

export interface DraftBranchChange {
	readonly type: 'create' | 'update' | 'delete' | 'addCommit' | 'removeCommit';
	readonly branchId: string;
	readonly data?: Partial<DraftBranch>;
	readonly commitId?: string;
}

export interface BranchValidationResult {
	readonly isValid: boolean;
	readonly errors: string[];
	readonly warnings: string[];
}

export function createDraftBranch(
	id: string,
	name: string,
	baseBranch: string,
	baseCommit: string,
	options?: {
		commitIds?: string[];
		isDefault?: boolean;
		color?: string;
		description?: string;
	},
): DraftBranch {
	return {
		id: id,
		name: name,
		baseBranch: baseBranch,
		baseCommit: baseCommit,
		commitIds: options?.commitIds ?? [],
		isDefault: options?.isDefault ?? false,
		color: options?.color,
		description: options?.description,
		timestamp: Date.now(),
	};
}

export function validateDraftBranch(branch: DraftBranch, existingBranches: string[]): BranchValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Validate branch name
	if (!branch.name.trim()) {
		errors.push('Branch name cannot be empty');
	} else if (!/^[a-zA-Z0-9/_-]+$/.test(branch.name)) {
		errors.push('Branch name contains invalid characters');
	} else if (existingBranches.includes(branch.name)) {
		errors.push(`Branch name '${branch.name}' already exists`);
	}

	// Validate base branch
	if (!branch.baseBranch.trim()) {
		errors.push('Base branch cannot be empty');
	}

	// Validate base commit
	if (!branch.baseCommit.trim()) {
		errors.push('Base commit cannot be empty');
	}

	// Warn if no commits
	if (branch.commitIds.length === 0) {
		warnings.push('Branch has no commits assigned');
	}

	return {
		isValid: errors.length === 0,
		errors: errors,
		warnings: warnings,
	};
}

export function updateDraftBranch(branch: DraftBranch, changes: Partial<DraftBranch>): DraftBranch {
	return {
		...branch,
		...changes,
		timestamp: Date.now(),
	};
}

export function addCommitToBranch(branch: DraftBranch, commitId: string): DraftBranch {
	if (branch.commitIds.includes(commitId)) {
		return branch;
	}

	return {
		...branch,
		commitIds: [...branch.commitIds, commitId],
		timestamp: Date.now(),
	};
}

export function removeCommitFromBranch(branch: DraftBranch, commitId: string): DraftBranch {
	return {
		...branch,
		commitIds: branch.commitIds.filter(id => id !== commitId),
		timestamp: Date.now(),
	};
}

export function reorderCommitsInBranch(branch: DraftBranch, commitIds: string[]): DraftBranch {
	return {
		...branch,
		commitIds: commitIds,
		timestamp: Date.now(),
	};
}

// Predefined branch colors for visual distinction
export const branchColors = [
	'#007acc', // Blue
	'#28a745', // Green
	'#ffc107', // Yellow
	'#dc3545', // Red
	'#6f42c1', // Purple
	'#fd7e14', // Orange
	'#20c997', // Teal
	'#e83e8c', // Pink
] as const;

export function getNextBranchColor(existingBranches: DraftBranch[]): string {
	const usedColors = new Set(existingBranches.map(b => b.color).filter(Boolean));
	return branchColors.find(color => !usedColors.has(color)) ?? branchColors[0];
}
