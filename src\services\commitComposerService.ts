import type { CancellationToken, Disposable } from 'vscode';
import type { Container } from '../container';
import type { GitFileChange } from '../git/models/file';
import type { GitReference } from '../git/models/reference';
import type { Repository } from '../git/models/repository';
import { uncommitted } from '../git/models/revision';
import { createReference } from '../git/utils/reference.utils';
import type { ComposerExecutionResult, ComposerState } from '../models/commitComposer/composerState';
import {
	addToHistory,
	createComposerState,
	getBranchById,
	getCommitById,
	updateComposerState,
} from '../models/commitComposer/composerState';
import type { DraftBranch } from '../models/commitComposer/draftBranch';
import {
	createDraftBranch,
	getNextBranchColor,
	updateDraftBranch,
	validateDraftBranch,
} from '../models/commitComposer/draftBranch';
import type { DraftCommit, DraftHunk } from '../models/commitComposer/draftCommit';
import { createDraftCommit, updateDraftCommit, validateDraftCommit } from '../models/commitComposer/draftCommit';
import { Logger } from '../system/logger';
import type { ExecutionPlan } from './commitComposerExecutor';
import { CommitComposerExecutor } from './commitComposerExecutor';
import type { ConflictDetectionResult, ValidationResult } from './commitComposerValidator';
import { CommitComposerValidator } from './commitComposerValidator';
import { DiffParser } from './diffParser';

export class CommitComposerService implements Disposable {
	private readonly _disposables: Disposable[] = [];
	private readonly _activeSessions = new Map<string, ComposerState>();

	constructor(private readonly container: Container) {}

	dispose(): void {
		this._disposables.forEach(d => d.dispose());
		this._activeSessions.clear();
	}

	async createSession(
		repository: Repository,
		sourceType: 'workingChanges' | 'commitRange',
		options?: {
			sourceRef?: GitReference;
			targetRef?: GitReference;
			cancellation?: CancellationToken;
		},
	): Promise<ComposerState> {
		const sessionId = `${repository.path}-${Date.now()}`;

		try {
			// Analyze the source and generate initial draft commits
			const { commits, hunks } = await this.analyzeSource(repository, sourceType, options);

			// Create default branch
			const defaultBranch = createDraftBranch('default', 'main-composition', 'HEAD', 'HEAD', {
				commitIds: commits.map(c => c.id),
				isDefault: true,
				color: getNextBranchColor([]),
				description: 'Main composition branch',
			});

			const state = createComposerState(sessionId, repository, sourceType, {
				sourceRef: options?.sourceRef,
				targetRef: options?.targetRef,
				commits: commits,
				branches: [defaultBranch],
				unassignedHunks: hunks,
			});

			this._activeSessions.set(sessionId, state);
			return state;
		} catch (error) {
			Logger.error(error, 'CommitComposerService', 'createSession');
			throw error;
		}
	}

	getSession(sessionId: string): ComposerState | undefined {
		return this._activeSessions.get(sessionId);
	}

	updateSession(sessionId: string, changes: Partial<ComposerState>, description?: string): ComposerState {
		const currentState = this._activeSessions.get(sessionId);
		if (!currentState) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const newState = updateComposerState(currentState, changes, description);
		this._activeSessions.set(sessionId, newState);
		return newState;
	}

	updateCommit(sessionId: string, commitId: string, changes: Partial<DraftCommit>): DraftCommit {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commit = getCommitById(state, commitId);
		if (!commit) {
			throw new Error(`Commit not found: ${commitId}`);
		}

		const updatedCommit = updateDraftCommit(commit, changes);
		const validation = validateDraftCommit(updatedCommit);

		if (!validation.isValid) {
			throw new Error(`Invalid commit: ${validation.errors.join(', ')}`);
		}

		const updatedCommits = state.commits.map(c => (c.id === commitId ? updatedCommit : c));
		this.updateSession(sessionId, { commits: updatedCommits }, `Updated commit: ${commitId}`);

		return updatedCommit;
	}

	createCommit(
		sessionId: string,
		message: string,
		options?: {
			description?: string;
			afterCommitId?: string;
			files?: GitFileChange[];
			hunks?: DraftHunk[];
		},
	): DraftCommit {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitId = `commit-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const insertIndex = options?.afterCommitId
			? state.commits.findIndex(c => c.id === options.afterCommitId) + 1
			: state.commits.length;

		const newCommit = createDraftCommit(
			commitId,
			insertIndex,
			message,
			options?.files ?? [],
			options?.hunks ?? [],
			{
				description: options?.description,
				isGenerated: false,
			},
		);

		const updatedCommits = [...state.commits];
		updatedCommits.splice(insertIndex, 0, newCommit);

		// Update indices for commits after the insertion point
		for (let i = insertIndex + 1; i < updatedCommits.length; i++) {
			updatedCommits[i] = updateDraftCommit(updatedCommits[i], { index: i });
		}

		this.updateSession(sessionId, { commits: updatedCommits }, `Created commit: ${message}`);
		return newCommit;
	}

	deleteCommit(sessionId: string, commitId: string): void {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitIndex = state.commits.findIndex(c => c.id === commitId);
		if (commitIndex === -1) {
			throw new Error(`Commit not found: ${commitId}`);
		}

		const updatedCommits = state.commits.filter(c => c.id !== commitId);

		// Update indices for remaining commits
		for (let i = commitIndex; i < updatedCommits.length; i++) {
			updatedCommits[i] = updateDraftCommit(updatedCommits[i], { index: i });
		}

		// Remove commit from branches
		const updatedBranches = state.branches.map(branch => ({
			...branch,
			commitIds: branch.commitIds.filter(id => id !== commitId),
		}));

		// Move hunks to unassigned
		const commit = state.commits[commitIndex];
		const additionalUnassignedHunks = commit.hunks.map(hunk => ({
			...hunk,
			commitId: undefined,
		}));

		this.updateSession(
			sessionId,
			{
				commits: updatedCommits,
				branches: updatedBranches,
				unassignedHunks: [...state.unassignedHunks, ...additionalUnassignedHunks],
			},
			`Deleted commit: ${commitId}`,
		);
	}

	reorderCommits(sessionId: string, commitIds: string[]): void {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitMap = new Map(state.commits.map(c => [c.id, c]));
		const reorderedCommits = commitIds
			.map(id => commitMap.get(id))
			.filter((commit): commit is DraftCommit => commit !== undefined)
			.map((commit, index) => updateDraftCommit(commit, { index: index }));

		this.updateSession(sessionId, { commits: reorderedCommits }, 'Reordered commits');
	}

	moveHunk(sessionId: string, hunkId: string, targetCommitId: string): void {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		// Find the hunk in commits or unassigned hunks
		let sourceCommit: DraftCommit | undefined;
		let hunk: DraftHunk | undefined;

		for (const commit of state.commits) {
			const foundHunk = commit.hunks.find(h => h.id === hunkId);
			if (foundHunk) {
				sourceCommit = commit;
				hunk = foundHunk;
				break;
			}
		}

		if (!hunk) {
			hunk = state.unassignedHunks.find(h => h.id === hunkId);
		}

		if (!hunk) {
			throw new Error(`Hunk not found: ${hunkId}`);
		}

		const targetCommit = getCommitById(state, targetCommitId);
		if (!targetCommit) {
			throw new Error(`Target commit not found: ${targetCommitId}`);
		}

		// Remove hunk from source
		let updatedCommits = state.commits;
		let updatedUnassignedHunks = state.unassignedHunks;

		if (sourceCommit) {
			updatedCommits = state.commits.map(c =>
				c.id === sourceCommit.id ? updateDraftCommit(c, { hunks: c.hunks.filter(h => h.id !== hunkId) }) : c,
			);
		} else {
			updatedUnassignedHunks = state.unassignedHunks.filter(h => h.id !== hunkId);
		}

		// Add hunk to target
		const movedHunk = { ...hunk, commitId: targetCommitId };
		updatedCommits = updatedCommits.map(c =>
			c.id === targetCommitId ? updateDraftCommit(c, { hunks: [...c.hunks, movedHunk] }) : c,
		);

		this.updateSession(
			sessionId,
			{
				commits: updatedCommits,
				unassignedHunks: updatedUnassignedHunks,
			},
			`Moved hunk to commit: ${targetCommitId}`,
		);
	}

	private async analyzeSource(
		repository: Repository,
		sourceType: 'workingChanges' | 'commitRange',
		options?: {
			sourceRef?: GitReference;
			targetRef?: GitReference;
			cancellation?: CancellationToken;
		},
	): Promise<{ commits: DraftCommit[]; hunks: DraftHunk[] }> {
		// This is a simplified implementation - in reality, this would:
		// 1. Get the diff/changes from the repository
		// 2. Use AI to analyze and generate logical commit groupings
		// 3. Parse hunks and associate them with commits

		// For now, return a basic structure
		const commits: DraftCommit[] = [];
		const hunks: DraftHunk[] = [];

		if (sourceType === 'workingChanges') {
			// Analyze working directory changes
			const status = await repository.git.status.getStatus();
			const changes = status?.files ?? [];

			if (changes.length > 0) {
				// Generate AI-powered commit suggestions
				const aiResult = await this.container.ai.generateRebase(
					repository,
					'HEAD',
					uncommitted,
					{ source: 'ai' },
					{ generateCommits: true, cancellation: options?.cancellation },
				);

				if (aiResult && aiResult !== 'cancelled') {
					// Parse the diff to extract hunks
					const { hunks: parsedHunks } = DiffParser.extractHunksFromAIResult(aiResult, repository.uri);

					// Convert AI result to draft commits and assign hunks
					for (let i = 0; i < aiResult.commits.length; i++) {
						const aiCommit = aiResult.commits[i];

						// Find hunks for this commit based on AI result
						const commitHunks: DraftHunk[] = [];
						for (const hunkRef of aiCommit.hunks) {
							const hunkIndex = hunkRef.hunk;
							if (hunkIndex < parsedHunks.length) {
								const hunk = parsedHunks[hunkIndex];
								const assignedHunk = { ...hunk, commitId: `ai-commit-${i}` };
								commitHunks.push(assignedHunk);
							}
						}

						// Collect files from hunks
						const commitFiles = new Set<string>();
						for (const hunk of commitHunks) {
							commitFiles.add(hunk.fileUri);
						}

						const commit = createDraftCommit(
							`ai-commit-${i}`,
							i,
							aiCommit.message,
							Array.from(commitFiles).map(uri => ({
								uri: { toString: () => uri } as any,
								status: 'M', // Modified - would need proper status detection
							})) as GitFileChange[],
							commitHunks,
							{
								description: aiCommit.explanation,
								isGenerated: true,
							},
						);
						commits.push(commit);
					}

					// Add any unassigned hunks
					const assignedHunkIds = new Set(commits.flatMap(c => c.hunks.map(h => h.id)));
					const unassignedHunks = parsedHunks.filter(h => !assignedHunkIds.has(h.id));
					hunks.push(...unassignedHunks);
				}
			}
		} else if (sourceType === 'commitRange') {
			// Analyze commit range changes
			if (options?.sourceRef && options?.targetRef) {
				// Get the diff between the two references
				const diff = await repository.git.diff.getDiff?.(options.sourceRef.ref, options.targetRef.ref, {
					notation: '...',
				});

				if (diff?.contents) {
					// For commit range, we could either:
					// 1. Use existing commits as a starting point
					// 2. Generate new commits from the combined diff
					// For now, let's create a single commit representing the range
					const commit = createDraftCommit(
						'range-commit-0',
						0,
						`Changes from ${options.targetRef.name} to ${options.sourceRef.name}`,
						[], // Files will be populated from diff analysis
						[], // Hunks will be populated from diff analysis
						{
							description: `Combined changes from commit range ${options.targetRef.ref}..${options.sourceRef.ref}`,
							isGenerated: true,
						},
					);
					commits.push(commit);
				}
			}
		}

		return { commits: commits, hunks: hunks };
	}

	async executeComposition(
		sessionId: string,
		options?: { dryRun?: boolean; createBackup?: boolean },
	): Promise<ComposerExecutionResult> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		// Validate the composition
		const validation = this.validateSession(sessionId);
		if (!validation.isValid) {
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: validation.errors.map(e => e.message),
			};
		}

		// Check for conflicts
		const conflicts = this.detectConflicts(sessionId);
		if (conflicts.hasConflicts) {
			const errorConflicts = conflicts.conflicts.filter(c => c.severity === 'error');
			if (errorConflicts.length > 0) {
				return {
					success: false,
					createdCommits: [],
					createdBranches: [],
					errors: errorConflicts.map(c => c.message),
				};
			}
		}

		try {
			// Mark the session as executing
			this.updateSession(sessionId, { isExecuting: true }, 'Starting composition execution');

			// Execute the composition using the executor
			const executor = new CommitComposerExecutor(this.container);
			const result = await executor.execute(state, {
				dryRun: options?.dryRun,
				createBackup: options?.createBackup,
			});

			// Mark execution as complete
			this.updateSession(
				sessionId,
				{ isExecuting: false, hasUnsavedChanges: false },
				'Composition execution completed',
			);

			return result;
		} catch (error) {
			// Mark execution as failed
			this.updateSession(sessionId, { isExecuting: false }, 'Composition execution failed');

			Logger.error(error, 'CommitComposerService', 'executeComposition');
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	createBranch(
		sessionId: string,
		name: string,
		baseBranch: string,
		baseCommit: string,
		options?: {
			description?: string;
			commitIds?: string[];
		},
	): DraftBranch {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const branchId = `branch-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const newBranch = createDraftBranch(branchId, name, baseBranch, baseCommit, {
			commitIds: options?.commitIds ?? [],
			isDefault: false,
			color: getNextBranchColor(state.branches),
			description: options?.description,
		});

		const validation = validateDraftBranch(
			newBranch,
			state.branches.map(b => b.name),
		);
		if (!validation.isValid) {
			throw new Error(`Invalid branch: ${validation.errors.join(', ')}`);
		}

		const updatedBranches = [...state.branches, newBranch];
		this.updateSession(sessionId, { branches: updatedBranches }, `Created branch: ${name}`);

		return newBranch;
	}

	updateBranch(sessionId: string, branchId: string, changes: Partial<DraftBranch>): DraftBranch {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const branch = getBranchById(state, branchId);
		if (!branch) {
			throw new Error(`Branch not found: ${branchId}`);
		}

		const updatedBranch = updateDraftBranch(branch, changes);
		const validation = validateDraftBranch(
			updatedBranch,
			state.branches.filter(b => b.id !== branchId).map(b => b.name),
		);

		if (!validation.isValid) {
			throw new Error(`Invalid branch: ${validation.errors.join(', ')}`);
		}

		const updatedBranches = state.branches.map(b => (b.id === branchId ? updatedBranch : b));
		this.updateSession(sessionId, { branches: updatedBranches }, `Updated branch: ${branchId}`);

		return updatedBranch;
	}

	squashCommits(sessionId: string, commitIds: string[], newMessage: string): DraftCommit {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		if (commitIds.length < 2) {
			throw new Error('At least two commits are required for squashing');
		}

		// Find all commits to squash
		const commitsToSquash = commitIds
			.map(id => getCommitById(state, id))
			.filter((commit): commit is DraftCommit => commit !== undefined);

		if (commitsToSquash.length !== commitIds.length) {
			throw new Error('Some commits not found');
		}

		// Sort by index to maintain order
		commitsToSquash.sort((a, b) => a.index - b.index);

		// Create new squashed commit
		const firstCommit = commitsToSquash[0];
		const allFiles = commitsToSquash.flatMap(c => c.files);
		const allHunks = commitsToSquash.flatMap(c => c.hunks);

		const squashedCommitId = `squashed-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const squashedCommit = createDraftCommit(
			squashedCommitId,
			firstCommit.index,
			newMessage,
			allFiles,
			allHunks.map(h => ({ ...h, commitId: squashedCommitId })),
			{
				description: `Squashed ${commitsToSquash.length} commits`,
				isGenerated: false,
			},
		);

		// Remove original commits and insert squashed commit
		const updatedCommits = state.commits.filter(c => !commitIds.includes(c.id));
		updatedCommits.splice(firstCommit.index, 0, squashedCommit);

		// Update indices for commits after the squashed commit
		for (let i = firstCommit.index + 1; i < updatedCommits.length; i++) {
			updatedCommits[i] = updateDraftCommit(updatedCommits[i], { index: i });
		}

		// Update branch references
		const updatedBranches = state.branches.map(branch => ({
			...branch,
			commitIds: branch.commitIds
				.filter(id => !commitIds.includes(id))
				.map(id => (id === commitIds[0] ? squashedCommitId : id)),
		}));

		this.updateSession(
			sessionId,
			{
				commits: updatedCommits,
				branches: updatedBranches,
			},
			`Squashed ${commitsToSquash.length} commits`,
		);

		return squashedCommit;
	}

	undoLastAction(sessionId: string): ComposerState | undefined {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		if (state.historyIndex <= 0) {
			return undefined; // No history to undo
		}

		const previousSnapshot = state.history[state.historyIndex - 1];
		const restoredState: ComposerState = {
			...state,
			commits: previousSnapshot.commits,
			branches: previousSnapshot.branches,
			unassignedHunks: previousSnapshot.unassignedHunks,
			historyIndex: state.historyIndex - 1,
			hasUnsavedChanges: true,
			timestamp: Date.now(),
		};

		this._activeSessions.set(sessionId, restoredState);
		return restoredState;
	}

	redoLastAction(sessionId: string): ComposerState | undefined {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		if (state.historyIndex >= state.history.length - 1) {
			return undefined; // No future history to redo
		}

		const nextSnapshot = state.history[state.historyIndex + 1];
		const restoredState: ComposerState = {
			...state,
			commits: nextSnapshot.commits,
			branches: nextSnapshot.branches,
			unassignedHunks: nextSnapshot.unassignedHunks,
			historyIndex: state.historyIndex + 1,
			hasUnsavedChanges: true,
			timestamp: Date.now(),
		};

		this._activeSessions.set(sessionId, restoredState);
		return restoredState;
	}

	/**
	 * Validate a composition session
	 */
	validateSession(sessionId: string): ValidationResult {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		return CommitComposerValidator.validate(state);
	}

	/**
	 * Detect conflicts in a composition session
	 */
	detectConflicts(sessionId: string): ConflictDetectionResult {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		return CommitComposerValidator.detectConflicts(state);
	}

	/**
	 * Get comprehensive validation and conflict information
	 */
	getSessionHealth(sessionId: string): {
		validation: ValidationResult;
		conflicts: ConflictDetectionResult;
		canExecute: boolean;
	} {
		const validation = this.validateSession(sessionId);
		const conflicts = this.detectConflicts(sessionId);

		return {
			validation: validation,
			conflicts: conflicts,
			canExecute: validation.isValid && !conflicts.hasConflicts,
		};
	}

	/**
	 * Get an execution plan for a composition
	 */
	getExecutionPlan(sessionId: string): ExecutionPlan {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const executor = new CommitComposerExecutor(this.container);
		return executor.createExecutionPlan(state);
	}

	closeSession(sessionId: string): void {
		this._activeSessions.delete(sessionId);
	}
}
