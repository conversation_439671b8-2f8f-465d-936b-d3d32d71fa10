import type { CancellationToken, Disposable } from 'vscode';
import type { Container } from '../container';
import type { GitFileChange } from '../git/models/file';
import type { GitReference } from '../git/models/reference';
import type { Repository } from '../git/models/repository';
import { uncommitted } from '../git/models/revision';
import { createReference } from '../git/utils/reference.utils';
import type { ComposerExecutionResult, ComposerState } from '../models/commitComposer/composerState';
import {
	addToHistory,
	createComposerState,
	getBranchById,
	getCommitById,
	updateComposerState,
	validateComposerState,
} from '../models/commitComposer/composerState';
import type { DraftBranch } from '../models/commitComposer/draftBranch';
import {
	createDraftBranch,
	getNextBranchColor,
	updateDraftBranch,
	validateDraftBranch,
} from '../models/commitComposer/draftBranch';
import type { DraftCommit, DraftHunk } from '../models/commitComposer/draftCommit';
import {
	createDraftCommit,
	createDraftHunk,
	updateDraftCommit,
	validateDraftCommit,
} from '../models/commitComposer/draftCommit';
import { Logger } from '../system/logger';

export class CommitComposerService implements Disposable {
	private readonly _disposables: Disposable[] = [];
	private readonly _activeSessions = new Map<string, ComposerState>();

	constructor(private readonly container: Container) {}

	dispose(): void {
		this._disposables.forEach(d => d.dispose());
		this._activeSessions.clear();
	}

	async createSession(
		repository: Repository,
		sourceType: 'workingChanges' | 'commitRange',
		options?: {
			sourceRef?: GitReference;
			targetRef?: GitReference;
			cancellation?: CancellationToken;
		},
	): Promise<ComposerState> {
		const sessionId = `${repository.path}-${Date.now()}`;

		try {
			// Analyze the source and generate initial draft commits
			const { commits, hunks } = await this.analyzeSource(repository, sourceType, options);

			// Create default branch
			const defaultBranch = createDraftBranch('default', 'main-composition', 'HEAD', 'HEAD', {
				commitIds: commits.map(c => c.id),
				isDefault: true,
				color: getNextBranchColor([]),
				description: 'Main composition branch',
			});

			const state = createComposerState(sessionId, repository, sourceType, {
				sourceRef: options?.sourceRef,
				targetRef: options?.targetRef,
				commits: commits,
				branches: [defaultBranch],
				unassignedHunks: hunks,
			});

			this._activeSessions.set(sessionId, state);
			return state;
		} catch (error) {
			Logger.error(error, 'CommitComposerService', 'createSession');
			throw error;
		}
	}

	getSession(sessionId: string): ComposerState | undefined {
		return this._activeSessions.get(sessionId);
	}

	updateSession(sessionId: string, changes: Partial<ComposerState>, description?: string): ComposerState {
		const currentState = this._activeSessions.get(sessionId);
		if (!currentState) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const newState = updateComposerState(currentState, changes, description);
		this._activeSessions.set(sessionId, newState);
		return newState;
	}

	async updateCommit(sessionId: string, commitId: string, changes: Partial<DraftCommit>): Promise<DraftCommit> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commit = getCommitById(state, commitId);
		if (!commit) {
			throw new Error(`Commit not found: ${commitId}`);
		}

		const updatedCommit = updateDraftCommit(commit, changes);
		const validation = validateDraftCommit(updatedCommit);

		if (!validation.isValid) {
			throw new Error(`Invalid commit: ${validation.errors.join(', ')}`);
		}

		const updatedCommits = state.commits.map(c => (c.id === commitId ? updatedCommit : c));
		this.updateSession(sessionId, { commits: updatedCommits }, `Updated commit: ${commitId}`);

		return updatedCommit;
	}

	async createCommit(
		sessionId: string,
		message: string,
		options?: {
			description?: string;
			afterCommitId?: string;
			files?: GitFileChange[];
			hunks?: DraftHunk[];
		},
	): Promise<DraftCommit> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitId = `commit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
		const insertIndex = options?.afterCommitId
			? state.commits.findIndex(c => c.id === options.afterCommitId) + 1
			: state.commits.length;

		const newCommit = createDraftCommit(
			commitId,
			insertIndex,
			message,
			options?.files ?? [],
			options?.hunks ?? [],
			{
				description: options?.description,
				isGenerated: false,
			},
		);

		const updatedCommits = [...state.commits];
		updatedCommits.splice(insertIndex, 0, newCommit);

		// Update indices for commits after the insertion point
		for (let i = insertIndex + 1; i < updatedCommits.length; i++) {
			updatedCommits[i] = updateDraftCommit(updatedCommits[i], { index: i });
		}

		this.updateSession(sessionId, { commits: updatedCommits }, `Created commit: ${message}`);
		return newCommit;
	}

	async deleteCommit(sessionId: string, commitId: string): Promise<void> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitIndex = state.commits.findIndex(c => c.id === commitId);
		if (commitIndex === -1) {
			throw new Error(`Commit not found: ${commitId}`);
		}

		const updatedCommits = state.commits.filter(c => c.id !== commitId);

		// Update indices for remaining commits
		for (let i = commitIndex; i < updatedCommits.length; i++) {
			updatedCommits[i] = updateDraftCommit(updatedCommits[i], { index: i });
		}

		// Remove commit from branches
		const updatedBranches = state.branches.map(branch => ({
			...branch,
			commitIds: branch.commitIds.filter(id => id !== commitId),
		}));

		// Move hunks to unassigned
		const commit = state.commits[commitIndex];
		const additionalUnassignedHunks = commit.hunks.map(hunk => ({
			...hunk,
			commitId: undefined,
		}));

		this.updateSession(
			sessionId,
			{
				commits: updatedCommits,
				branches: updatedBranches,
				unassignedHunks: [...state.unassignedHunks, ...additionalUnassignedHunks],
			},
			`Deleted commit: ${commitId}`,
		);
	}

	async reorderCommits(sessionId: string, commitIds: string[]): Promise<void> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		const commitMap = new Map(state.commits.map(c => [c.id, c]));
		const reorderedCommits = commitIds
			.map(id => commitMap.get(id))
			.filter((commit): commit is DraftCommit => commit !== undefined)
			.map((commit, index) => updateDraftCommit(commit, { index: index }));

		this.updateSession(sessionId, { commits: reorderedCommits }, 'Reordered commits');
	}

	async moveHunk(sessionId: string, hunkId: string, targetCommitId: string): Promise<void> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		// Find the hunk in commits or unassigned hunks
		let sourceCommit: DraftCommit | undefined;
		let hunk: DraftHunk | undefined;

		for (const commit of state.commits) {
			const foundHunk = commit.hunks.find(h => h.id === hunkId);
			if (foundHunk) {
				sourceCommit = commit;
				hunk = foundHunk;
				break;
			}
		}

		if (!hunk) {
			hunk = state.unassignedHunks.find(h => h.id === hunkId);
		}

		if (!hunk) {
			throw new Error(`Hunk not found: ${hunkId}`);
		}

		const targetCommit = getCommitById(state, targetCommitId);
		if (!targetCommit) {
			throw new Error(`Target commit not found: ${targetCommitId}`);
		}

		// Remove hunk from source
		let updatedCommits = state.commits;
		let updatedUnassignedHunks = state.unassignedHunks;

		if (sourceCommit) {
			updatedCommits = state.commits.map(c =>
				c.id === sourceCommit.id ? updateDraftCommit(c, { hunks: c.hunks.filter(h => h.id !== hunkId) }) : c,
			);
		} else {
			updatedUnassignedHunks = state.unassignedHunks.filter(h => h.id !== hunkId);
		}

		// Add hunk to target
		const movedHunk = { ...hunk, commitId: targetCommitId };
		updatedCommits = updatedCommits.map(c =>
			c.id === targetCommitId ? updateDraftCommit(c, { hunks: [...c.hunks, movedHunk] }) : c,
		);

		this.updateSession(
			sessionId,
			{
				commits: updatedCommits,
				unassignedHunks: updatedUnassignedHunks,
			},
			`Moved hunk to commit: ${targetCommitId}`,
		);
	}

	private async analyzeSource(
		repository: Repository,
		sourceType: 'workingChanges' | 'commitRange',
		options?: {
			sourceRef?: GitReference;
			targetRef?: GitReference;
			cancellation?: CancellationToken;
		},
	): Promise<{ commits: DraftCommit[]; hunks: DraftHunk[] }> {
		// This is a simplified implementation - in reality, this would:
		// 1. Get the diff/changes from the repository
		// 2. Use AI to analyze and generate logical commit groupings
		// 3. Parse hunks and associate them with commits

		// For now, return a basic structure
		const commits: DraftCommit[] = [];
		const hunks: DraftHunk[] = [];

		if (sourceType === 'workingChanges') {
			// Analyze working directory changes
			const changes = (await repository.getWorkingChanges?.()) ?? [];

			if (changes.length > 0) {
				// Generate AI-powered commit suggestions
				const aiResult = await this.container.ai.generateRebase(
					repository,
					'HEAD',
					uncommitted,
					{ source: 'commitComposer' },
					{ generateCommits: true, cancellation: options?.cancellation },
				);

				if (aiResult && aiResult !== 'cancelled') {
					// Convert AI result to draft commits
					for (let i = 0; i < aiResult.commits.length; i++) {
						const aiCommit = aiResult.commits[i];
						const commit = createDraftCommit(
							`ai-commit-${i}`,
							i,
							aiCommit.message,
							[], // Files will be populated from hunks
							[], // Hunks will be added separately
							{
								description: aiCommit.explanation,
								isGenerated: true,
							},
						);
						commits.push(commit);
					}
				}
			}
		}

		return { commits: commits, hunks: hunks };
	}

	async executeComposition(sessionId: string): Promise<ComposerExecutionResult> {
		const state = this.getSession(sessionId);
		if (!state) {
			throw new Error(`Session not found: ${sessionId}`);
		}

		// Validate the composition
		const errors = validateComposerState(state);
		if (errors.length > 0) {
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: errors,
			};
		}

		try {
			// This would implement the actual Git operations
			// For now, return a success result
			return {
				success: true,
				createdCommits: state.commits.map(c => c.id),
				createdBranches: state.branches.map(b => b.name),
				errors: [],
				undoCommand: 'gitlens.commitComposer.undo',
			};
		} catch (error) {
			Logger.error(error, 'CommitComposerService', 'executeComposition');
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}

	closeSession(sessionId: string): void {
		this._activeSessions.delete(sessionId);
	}
}
