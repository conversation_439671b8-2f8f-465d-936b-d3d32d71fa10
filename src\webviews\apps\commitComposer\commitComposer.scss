@import '../shared/base';
@import '../shared/buttons';
@import '../shared/utils';

.commit-composer {
	height: 100vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	background: var(--color-view-background);
	color: var(--color-view-foreground);
}

.commit-composer__container {
	display: flex;
	flex-direction: column;
	height: 100%;
}

// Header
.commit-composer__header {
	flex-shrink: 0;
	border-bottom: 1px solid var(--color-view-border);
	background: var(--color-view-header-background);
	padding: 12px 16px;
}

.commit-composer__header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.commit-composer__title {
	h1 {
		margin: 0;
		font-size: 1.2rem;
		font-weight: 600;
		display: flex;
		align-items: center;
		gap: 8px;
		
		code-icon {
			color: var(--color-icon-foreground);
		}
	}
}

.commit-composer__subtitle {
	margin-top: 4px;
	font-size: 0.9rem;
	color: var(--color-foreground-secondary);
}

.commit-composer__actions {
	display: flex;
	align-items: center;
	gap: 4px;
}

.commit-composer__action-btn {
	@include button-reset;
	padding: 6px 8px;
	border-radius: 4px;
	background: transparent;
	color: var(--color-foreground);
	border: 1px solid transparent;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&:hover {
		background: var(--color-button-secondary-hover-background);
		border-color: var(--color-button-secondary-hover-border);
	}
	
	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}
	
	code-icon {
		font-size: 14px;
	}
}

.commit-composer__action-separator {
	width: 1px;
	height: 20px;
	background: var(--color-view-border);
	margin: 0 4px;
}

// Main content
.commit-composer__main {
	flex: 1;
	display: grid;
	grid-template-columns: 1fr 1fr 300px;
	gap: 1px;
	background: var(--color-view-border);
	overflow: hidden;
}

// Panels
.commit-composer__panel {
	background: var(--color-view-background);
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.commit-composer__panel-header {
	flex-shrink: 0;
	padding: 12px 16px;
	border-bottom: 1px solid var(--color-view-border);
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: var(--color-view-header-background);
	
	h2 {
		margin: 0;
		font-size: 1rem;
		font-weight: 600;
		display: flex;
		align-items: center;
		gap: 8px;
		
		code-icon {
			color: var(--color-icon-foreground);
		}
	}
}

.commit-composer__count {
	background: var(--color-badge-background);
	color: var(--color-badge-foreground);
	padding: 2px 6px;
	border-radius: 10px;
	font-size: 0.8rem;
	font-weight: 500;
	margin-left: 8px;
}

.commit-composer__panel-actions {
	display: flex;
	align-items: center;
	gap: 4px;
}

.commit-composer__panel-action {
	@include button-reset;
	padding: 4px 6px;
	border-radius: 3px;
	background: transparent;
	color: var(--color-foreground);
	border: 1px solid transparent;
	cursor: pointer;
	
	&:hover {
		background: var(--color-button-secondary-hover-background);
		border-color: var(--color-button-secondary-hover-border);
	}
	
	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}
	
	code-icon {
		font-size: 12px;
	}
}

.commit-composer__panel-content {
	flex: 1;
	overflow: auto;
	padding: 8px;
}

// Empty states
.commit-composer__empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	color: var(--color-foreground-secondary);
	text-align: center;
	
	code-icon {
		margin-bottom: 16px;
		opacity: 0.5;
	}
	
	p {
		margin: 0 0 16px 0;
		font-size: 0.9rem;
	}
}

.commit-composer__empty-action {
	@include button-reset;
	padding: 8px 16px;
	background: var(--color-button-primary-background);
	color: var(--color-button-primary-foreground);
	border: 1px solid var(--color-button-primary-border);
	border-radius: 4px;
	cursor: pointer;
	font-size: 0.9rem;
	
	&:hover {
		background: var(--color-button-primary-hover-background);
		border-color: var(--color-button-primary-hover-border);
	}
}

// Commits panel
.commit-composer__commits-list {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.commit-composer__commit-item {
	border: 1px solid var(--color-view-border);
	border-radius: 6px;
	background: var(--color-view-background);
	overflow: hidden;
	cursor: pointer;
	transition: all 0.2s ease;
	
	&:hover {
		border-color: var(--color-focus-border);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
	
	&--selected {
		border-color: var(--color-focus-border);
		background: var(--color-list-active-selection-background);
		
		.commit-composer__commit-message-input {
			background: transparent;
		}
	}
}

.commit-composer__commit-header {
	display: flex;
	align-items: flex-start;
	padding: 12px;
	gap: 8px;
}

.commit-composer__commit-handle {
	color: var(--color-foreground-secondary);
	cursor: grab;
	padding: 2px;
	
	&:active {
		cursor: grabbing;
	}
	
	code-icon {
		font-size: 12px;
	}
}

.commit-composer__commit-info {
	flex: 1;
	min-width: 0;
}

.commit-composer__commit-message {
	margin-bottom: 8px;
}

.commit-composer__commit-message-input {
	width: 100%;
	background: transparent;
	border: none;
	color: var(--color-foreground);
	font-size: 0.95rem;
	font-weight: 500;
	padding: 0;
	outline: none;
	
	&::placeholder {
		color: var(--color-input-placeholder-foreground);
	}
	
	&:focus {
		background: var(--color-input-background);
		padding: 4px 8px;
		border-radius: 3px;
		border: 1px solid var(--color-focus-border);
	}
}

.commit-composer__commit-meta {
	display: flex;
	align-items: center;
	gap: 12px;
	font-size: 0.8rem;
	color: var(--color-foreground-secondary);
}

.commit-composer__commit-index {
	font-weight: 600;
	color: var(--color-foreground);
}

.commit-composer__commit-ai-badge {
	background: var(--color-badge-background);
	color: var(--color-badge-foreground);
	padding: 1px 4px;
	border-radius: 3px;
	font-size: 0.7rem;
	font-weight: 600;
	text-transform: uppercase;
}

.commit-composer__commit-actions {
	display: flex;
	align-items: center;
	gap: 4px;
	opacity: 0;
	transition: opacity 0.2s ease;
	
	.commit-composer__commit-item:hover & {
		opacity: 1;
	}
}

.commit-composer__commit-action {
	@include button-reset;
	padding: 4px;
	border-radius: 3px;
	background: transparent;
	color: var(--color-foreground-secondary);
	border: 1px solid transparent;
	cursor: pointer;
	
	&:hover {
		background: var(--color-button-secondary-hover-background);
		border-color: var(--color-button-secondary-hover-border);
		color: var(--color-foreground);
	}
	
	code-icon {
		font-size: 12px;
	}
}

.commit-composer__commit-description {
	padding: 0 12px 12px 32px;
	border-top: 1px solid var(--color-view-border);
	margin-top: 8px;
	padding-top: 8px;
}

.commit-composer__commit-description-input {
	width: 100%;
	background: var(--color-input-background);
	border: 1px solid var(--color-input-border);
	color: var(--color-input-foreground);
	font-size: 0.9rem;
	padding: 6px 8px;
	border-radius: 3px;
	outline: none;
	resize: vertical;
	min-height: 60px;
	font-family: inherit;
	
	&::placeholder {
		color: var(--color-input-placeholder-foreground);
	}
	
	&:focus {
		border-color: var(--color-focus-border);
	}
}

// Footer
.commit-composer__footer {
	flex-shrink: 0;
	border-top: 1px solid var(--color-view-border);
	background: var(--color-view-header-background);
	padding: 12px 16px;
}

.commit-composer__footer-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.commit-composer__status {
	display: flex;
	align-items: center;
	gap: 16px;
}

.commit-composer__validation {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 0.9rem;
}

.commit-composer__validation-icon {
	font-size: 14px;
	
	&[icon="check"] {
		color: var(--color-success-foreground);
	}
	
	&[icon="error"] {
		color: var(--color-error-foreground);
	}
}

.commit-composer__summary {
	font-size: 0.9rem;
	color: var(--color-foreground-secondary);
}

.commit-composer__footer-actions {
	display: flex;
	align-items: center;
	gap: 8px;
}

.commit-composer__footer-btn {
	@include button-reset;
	padding: 8px 16px;
	border-radius: 4px;
	font-size: 0.9rem;
	font-weight: 500;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 6px;
	
	&--secondary {
		background: var(--color-button-secondary-background);
		color: var(--color-button-secondary-foreground);
		border: 1px solid var(--color-button-secondary-border);
		
		&:hover {
			background: var(--color-button-secondary-hover-background);
			border-color: var(--color-button-secondary-hover-border);
		}
	}
	
	&--primary {
		background: var(--color-button-primary-background);
		color: var(--color-button-primary-foreground);
		border: 1px solid var(--color-button-primary-border);
		
		&:hover:not(:disabled) {
			background: var(--color-button-primary-hover-background);
			border-color: var(--color-button-primary-hover-border);
		}
		
		&:disabled {
			opacity: 0.5;
			cursor: not-allowed;
		}
	}
	
	code-icon {
		font-size: 14px;
	}
}

// Loading overlay
.commit-composer__loading {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.commit-composer__loading-content {
	background: var(--color-view-background);
	padding: 24px;
	border-radius: 8px;
	text-align: center;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
	
	p {
		margin: 16px 0 0 0;
		color: var(--color-foreground);
	}
}

.commit-composer__spinner {
	width: 32px;
	height: 32px;
	border: 3px solid var(--color-view-border);
	border-top: 3px solid var(--color-focus-border);
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 1200px) {
	.commit-composer__main {
		grid-template-columns: 1fr 1fr;
		
		.commit-composer__branches-panel {
			display: none;
		}
	}
}

@media (max-width: 800px) {
	.commit-composer__main {
		grid-template-columns: 1fr;
	}
	
	.commit-composer__header-content {
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;
	}
	
	.commit-composer__footer-content {
		flex-direction: column;
		align-items: stretch;
		gap: 12px;
	}
	
	.commit-composer__footer-actions {
		justify-content: stretch;
		
		.commit-composer__footer-btn {
			flex: 1;
			justify-content: center;
		}
	}
}
