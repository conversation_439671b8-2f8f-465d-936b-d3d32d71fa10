import type { Uri } from 'vscode';
import type { DraftHunk } from '../models/commitComposer/draftCommit';
import { createDraftHunk } from '../models/commitComposer/draftCommit';

export interface ParsedDiff {
	readonly fileUri: string;
	readonly oldPath?: string;
	readonly newPath?: string;
	readonly hunks: DraftHunk[];
	readonly isNew: boolean;
	readonly isDeleted: boolean;
	readonly isRenamed: boolean;
	readonly isBinary: boolean;
}

export interface DiffParseResult {
	readonly files: ParsedDiff[];
	readonly totalHunks: number;
}

/**
 * Parses a unified diff string and extracts hunks for commit composition
 */
export class DiffParser {
	/**
	 * Parse a unified diff string into structured data
	 */
	static parse(diffContent: string, baseUri?: Uri): DiffParseResult {
		const files: ParsedDiff[] = [];
		const lines = diffContent.split('\n');
		let currentFile: Partial<ParsedDiff> | null = null;
		let currentHunk: Partial<DraftHunk> | null = null;
		let hunkContent: string[] = [];
		let hunkIndex = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];

			// File header detection
			if (line.startsWith('diff --git')) {
				// Save previous file if exists
				if (currentFile && currentHunk) {
					this.finalizeHunk(currentFile, currentHunk, hunkContent, hunkIndex++);
				}
				if (currentFile) {
					files.push(currentFile as ParsedDiff);
				}

				// Start new file
				currentFile = this.parseFileHeader(line, baseUri);
				hunkIndex = 0;
				continue;
			}

			// File path headers
			if (line.startsWith('--- ') || line.startsWith('+++ ')) {
				if (currentFile) {
					this.updateFilePaths(currentFile, line);
				}
				continue;
			}

			// Hunk header detection
			if (line.startsWith('@@')) {
				// Save previous hunk if exists
				if (currentFile && currentHunk) {
					this.finalizeHunk(currentFile, currentHunk, hunkContent, hunkIndex++);
				}

				// Start new hunk
				currentHunk = this.parseHunkHeader(line, hunkIndex);
				hunkContent = [];
				continue;
			}

			// Binary file detection
			if (line.includes('Binary files') && currentFile) {
				currentFile.isBinary = true;
				continue;
			}

			// Hunk content
			if (currentHunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-'))) {
				hunkContent.push(line);
			}
		}

		// Finalize last hunk and file
		if (currentFile && currentHunk) {
			this.finalizeHunk(currentFile, currentHunk, hunkContent, hunkIndex);
		}
		if (currentFile) {
			files.push(currentFile as ParsedDiff);
		}

		return {
			files,
			totalHunks: files.reduce((sum, file) => sum + file.hunks.length, 0),
		};
	}

	private static parseFileHeader(line: string, baseUri?: Uri): Partial<ParsedDiff> {
		// Extract file paths from "diff --git a/path b/path"
		const match = line.match(/^diff --git a\/(.+) b\/(.+)$/);
		if (!match) {
			return {
				fileUri: '',
				hunks: [],
				isNew: false,
				isDeleted: false,
				isRenamed: false,
				isBinary: false,
			};
		}

		const oldPath = match[1];
		const newPath = match[2];
		const fileUri = baseUri ? baseUri.with({ path: `${baseUri.path}/${newPath}` }).toString() : newPath;

		return {
			fileUri,
			oldPath,
			newPath,
			hunks: [],
			isNew: false,
			isDeleted: false,
			isRenamed: oldPath !== newPath,
			isBinary: false,
		};
	}

	private static updateFilePaths(file: Partial<ParsedDiff>, line: string): void {
		if (line.startsWith('--- ')) {
			const path = line.substring(4);
			if (path === '/dev/null') {
				file.isNew = true;
			}
		} else if (line.startsWith('+++ ')) {
			const path = line.substring(4);
			if (path === '/dev/null') {
				file.isDeleted = true;
			}
		}
	}

	private static parseHunkHeader(line: string, index: number): Partial<DraftHunk> {
		// Parse "@@ -oldStart,oldLines +newStart,newLines @@"
		const match = line.match(/^@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);
		if (!match) {
			return {
				id: `hunk-${index}`,
				header: line,
				oldStart: 0,
				oldLines: 0,
				newStart: 0,
				newLines: 0,
				isSelected: false,
			};
		}

		const oldStart = parseInt(match[1], 10);
		const oldLines = match[2] ? parseInt(match[2], 10) : 1;
		const newStart = parseInt(match[3], 10);
		const newLines = match[4] ? parseInt(match[4], 10) : 1;

		return {
			id: `hunk-${index}`,
			header: line,
			oldStart,
			oldLines,
			newStart,
			newLines,
			isSelected: false,
		};
	}

	private static finalizeHunk(
		file: Partial<ParsedDiff>,
		hunk: Partial<DraftHunk>,
		content: string[],
		index: number,
	): void {
		if (!file.hunks) {
			file.hunks = [];
		}

		const finalHunk = createDraftHunk(
			hunk.id || `hunk-${index}`,
			file.fileUri || '',
			hunk.oldStart || 0,
			hunk.oldLines || 0,
			hunk.newStart || 0,
			hunk.newLines || 0,
			content.join('\n'),
			hunk.header || '',
			{
				isSelected: hunk.isSelected || false,
				commitId: hunk.commitId,
			},
		);

		file.hunks.push(finalHunk);
	}

	/**
	 * Extract hunks from AI rebase result and map them to files
	 */
	static extractHunksFromAIResult(
		aiResult: { diff: string; hunkMap: { index: number; hunkHeader: string }[] },
		baseUri?: Uri,
	): { hunks: DraftHunk[]; fileMap: Map<string, DraftHunk[]> } {
		const parseResult = this.parse(aiResult.diff, baseUri);
		const allHunks: DraftHunk[] = [];
		const fileMap = new Map<string, DraftHunk[]>();

		for (const file of parseResult.files) {
			fileMap.set(file.fileUri, file.hunks);
			allHunks.push(...file.hunks);
		}

		return { hunks: allHunks, fileMap };
	}

	/**
	 * Create hunks from working directory status
	 */
	static async createHunksFromStatus(
		files: { uri: Uri; status: string }[],
		getDiff: (uri: Uri) => Promise<string | undefined>,
	): Promise<DraftHunk[]> {
		const allHunks: DraftHunk[] = [];

		for (const file of files) {
			const diff = await getDiff(file.uri);
			if (diff) {
				const parseResult = this.parse(diff);
				const fileHunks = parseResult.files.find(f => f.fileUri === file.uri.toString())?.hunks || [];
				allHunks.push(...fileHunks);
			}
		}

		return allHunks;
	}

	/**
	 * Validate that hunks don't overlap within the same file
	 */
	static validateHunkConsistency(hunks: DraftHunk[]): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];
		const fileGroups = new Map<string, DraftHunk[]>();

		// Group hunks by file
		for (const hunk of hunks) {
			if (!fileGroups.has(hunk.fileUri)) {
				fileGroups.set(hunk.fileUri, []);
			}
			fileGroups.get(hunk.fileUri)!.push(hunk);
		}

		// Check for overlaps within each file
		for (const [fileUri, fileHunks] of fileGroups) {
			const sortedHunks = fileHunks.sort((a, b) => a.newStart - b.newStart);

			for (let i = 0; i < sortedHunks.length - 1; i++) {
				const current = sortedHunks[i];
				const next = sortedHunks[i + 1];

				const currentEnd = current.newStart + current.newLines;
				if (currentEnd > next.newStart) {
					errors.push(
						`Overlapping hunks in ${fileUri}: hunk ${current.id} (lines ${current.newStart}-${currentEnd}) overlaps with hunk ${next.id} (starts at line ${next.newStart})`,
					);
				}
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}
}
