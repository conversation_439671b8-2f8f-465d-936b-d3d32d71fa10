import type { Repository } from '../../git/models/repository';
import type { GitReference } from '../../git/models/reference';
import type { DraftBranch } from './draftBranch';
import type { DraftCommit, DraftHunk } from './draftCommit';

export interface ComposerState {
	readonly id: string;
	readonly repository: Repository;
	readonly sourceType: 'workingChanges' | 'commitRange';
	readonly sourceRef?: GitReference;
	readonly targetRef?: GitReference;
	readonly commits: DraftCommit[];
	readonly branches: DraftBranch[];
	readonly unassignedHunks: DraftHunk[];
	readonly selectedCommitId?: string;
	readonly selectedBranchId?: string;
	readonly isExecuting: boolean;
	readonly hasUnsavedChanges: boolean;
	readonly validationErrors: string[];
	readonly history: ComposerStateSnapshot[];
	readonly historyIndex: number;
	readonly timestamp: number;
}

export interface ComposerStateSnapshot {
	readonly commits: DraftCommit[];
	readonly branches: DraftBranch[];
	readonly unassignedHunks: DraftHunk[];
	readonly timestamp: number;
	readonly description: string;
}

export interface ComposerOperation {
	readonly type: 'commit' | 'branch' | 'hunk' | 'bulk';
	readonly action: string;
	readonly data: any;
	readonly description: string;
}

export interface ComposerExecutionResult {
	readonly success: boolean;
	readonly createdCommits: string[];
	readonly createdBranches: string[];
	readonly errors: string[];
	readonly undoCommand?: string;
}

export function createComposerState(
	id: string,
	repository: Repository,
	sourceType: 'workingChanges' | 'commitRange',
	options?: {
		sourceRef?: GitReference;
		targetRef?: GitReference;
		commits?: DraftCommit[];
		branches?: DraftBranch[];
		unassignedHunks?: DraftHunk[];
	}
): ComposerState {
	const initialState: ComposerState = {
		id,
		repository,
		sourceType,
		sourceRef: options?.sourceRef,
		targetRef: options?.targetRef,
		commits: options?.commits ?? [],
		branches: options?.branches ?? [],
		unassignedHunks: options?.unassignedHunks ?? [],
		selectedCommitId: undefined,
		selectedBranchId: undefined,
		isExecuting: false,
		hasUnsavedChanges: false,
		validationErrors: [],
		history: [],
		historyIndex: -1,
		timestamp: Date.now(),
	};

	return addToHistory(initialState, 'Initial state');
}

export function updateComposerState(
	state: ComposerState,
	changes: Partial<ComposerState>,
	description?: string
): ComposerState {
	const newState: ComposerState = {
		...state,
		...changes,
		hasUnsavedChanges: true,
		timestamp: Date.now(),
	};

	if (description) {
		return addToHistory(newState, description);
	}

	return newState;
}

export function addToHistory(state: ComposerState, description: string): ComposerState {
	const snapshot: ComposerStateSnapshot = {
		commits: state.commits,
		branches: state.branches,
		unassignedHunks: state.unassignedHunks,
		timestamp: Date.now(),
		description,
	};

	// Truncate history if we're not at the end
	const history = state.history.slice(0, state.historyIndex + 1);
	history.push(snapshot);

	// Limit history size
	const maxHistorySize = 50;
	if (history.length > maxHistorySize) {
		history.shift();
	}

	return {
		...state,
		history,
		historyIndex: history.length - 1,
		timestamp: Date.now(),
	};
}

export function canUndo(state: ComposerState): boolean {
	return state.historyIndex > 0;
}

export function canRedo(state: ComposerState): boolean {
	return state.historyIndex < state.history.length - 1;
}

export function undo(state: ComposerState): ComposerState {
	if (!canUndo(state)) {
		return state;
	}

	const newIndex = state.historyIndex - 1;
	const snapshot = state.history[newIndex];

	return {
		...state,
		commits: snapshot.commits,
		branches: snapshot.branches,
		unassignedHunks: snapshot.unassignedHunks,
		historyIndex: newIndex,
		hasUnsavedChanges: true,
		timestamp: Date.now(),
	};
}

export function redo(state: ComposerState): ComposerState {
	if (!canRedo(state)) {
		return state;
	}

	const newIndex = state.historyIndex + 1;
	const snapshot = state.history[newIndex];

	return {
		...state,
		commits: snapshot.commits,
		branches: snapshot.branches,
		unassignedHunks: snapshot.unassignedHunks,
		historyIndex: newIndex,
		hasUnsavedChanges: true,
		timestamp: Date.now(),
	};
}

export function validateComposerState(state: ComposerState): string[] {
	const errors: string[] = [];

	// Validate commits
	if (state.commits.length === 0) {
		errors.push('At least one commit is required');
	}

	// Validate commit references
	const commitIds = new Set(state.commits.map(c => c.id));
	for (const branch of state.branches) {
		for (const commitId of branch.commitIds) {
			if (!commitIds.has(commitId)) {
				errors.push(`Branch '${branch.name}' references non-existent commit: ${commitId}`);
			}
		}
	}

	// Validate hunk assignments
	for (const hunk of state.unassignedHunks) {
		if (hunk.commitId && !commitIds.has(hunk.commitId)) {
			errors.push(`Hunk references non-existent commit: ${hunk.commitId}`);
		}
	}

	return errors;
}

export function getCommitById(state: ComposerState, commitId: string): DraftCommit | undefined {
	return state.commits.find(c => c.id === commitId);
}

export function getBranchById(state: ComposerState, branchId: string): DraftBranch | undefined {
	return state.branches.find(b => b.id === branchId);
}

export function getCommitsByBranch(state: ComposerState, branchId: string): DraftCommit[] {
	const branch = getBranchById(state, branchId);
	if (!branch) return [];

	return branch.commitIds
		.map(id => getCommitById(state, id))
		.filter((commit): commit is DraftCommit => commit !== undefined);
}
