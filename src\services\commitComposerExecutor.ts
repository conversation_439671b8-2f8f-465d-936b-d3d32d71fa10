import type { CancellationToken } from 'vscode';
import type { Container } from '../container';
import type { Repository } from '../git/models/repository';
import type { ComposerExecutionResult, ComposerState } from '../models/commitComposer/composerState';
import type { DraftBranch } from '../models/commitComposer/draftBranch';
import type { DraftCommit } from '../models/commitComposer/draftCommit';
import { Logger } from '../system/logger';

export interface ExecutionOptions {
	readonly cancellation?: CancellationToken;
	readonly dryRun?: boolean;
	readonly createBackup?: boolean;
	readonly backupBranchName?: string;
}

export interface ExecutionStep {
	readonly type: 'backup' | 'branch' | 'commit' | 'cleanup';
	readonly description: string;
	readonly data?: any;
}

export interface ExecutionPlan {
	readonly steps: ExecutionStep[];
	readonly estimatedDuration: number;
	readonly risksAssessment: string[];
}

/**
 * Executes commit compositions by creating actual Git commits and branches
 */
export class CommitComposerExecutor {
	constructor(private readonly container: Container) {}

	/**
	 * Create an execution plan for a composition
	 */
	createExecutionPlan(state: ComposerState): ExecutionPlan {
		const steps: ExecutionStep[] = [];
		const risks: string[] = [];

		// Step 1: Create backup if needed
		if (state.commits.length > 0) {
			steps.push({
				type: 'backup',
				description: 'Create backup of current state',
				data: { branchName: `gitlens-composer-backup-${Date.now()}` },
			});
		}

		// Step 2: Create branches
		for (const branch of state.branches) {
			if (!branch.isDefault) {
				steps.push({
					type: 'branch',
					description: `Create branch: ${branch.name}`,
					data: { branch },
				});
			}
		}

		// Step 3: Create commits
		for (const commit of state.commits) {
			steps.push({
				type: 'commit',
				description: `Create commit: ${commit.message}`,
				data: { commit },
			});
		}

		// Step 4: Cleanup
		steps.push({
			type: 'cleanup',
			description: 'Clean up temporary files and state',
		});

		// Assess risks
		if (state.commits.length > 10) {
			risks.push('Large number of commits may take significant time');
		}

		if (state.branches.length > 1) {
			risks.push('Multiple branches will be created');
		}

		if (state.unassignedHunks.length > 0) {
			risks.push('Unassigned hunks will be ignored');
		}

		return {
			steps,
			estimatedDuration: steps.length * 2000, // Rough estimate: 2 seconds per step
			risksAssessment: risks,
		};
	}

	/**
	 * Execute a composition
	 */
	async execute(
		state: ComposerState,
		options?: ExecutionOptions,
	): Promise<ComposerExecutionResult> {
		const repository = state.repository;
		const createdCommits: string[] = [];
		const createdBranches: string[] = [];
		const errors: string[] = [];
		let backupBranchName: string | undefined;

		try {
			// Create backup if requested
			if (options?.createBackup !== false) {
				backupBranchName = options?.backupBranchName ?? `gitlens-composer-backup-${Date.now()}`;
				await this.createBackupBranch(repository, backupBranchName);
			}

			// Create branches first
			for (const branch of state.branches) {
				if (!branch.isDefault) {
					try {
						await this.createBranch(repository, branch, options);
						createdBranches.push(branch.name);
					} catch (error) {
						const message = error instanceof Error ? error.message : String(error);
						errors.push(`Failed to create branch ${branch.name}: ${message}`);
					}
				}
			}

			// Create commits
			for (const commit of state.commits) {
				try {
					const commitSha = await this.createCommit(repository, commit, state, options);
					if (commitSha) {
						createdCommits.push(commitSha);
					}
				} catch (error) {
					const message = error instanceof Error ? error.message : String(error);
					errors.push(`Failed to create commit "${commit.message}": ${message}`);
				}

				// Check for cancellation
				if (options?.cancellation?.isCancellationRequested) {
					errors.push('Execution was cancelled');
					break;
				}
			}

			const success = errors.length === 0;
			return {
				success,
				createdCommits,
				createdBranches,
				errors,
				undoCommand: success && backupBranchName ? 'gitlens.commitComposer.undo' : undefined,
			};
		} catch (error) {
			Logger.error(error, 'CommitComposerExecutor', 'execute');
			const message = error instanceof Error ? error.message : String(error);
			return {
				success: false,
				createdCommits,
				createdBranches,
				errors: [...errors, `Execution failed: ${message}`],
			};
		}
	}

	private async createBackupBranch(repository: Repository, branchName: string): Promise<void> {
		try {
			// Get current HEAD
			const head = await repository.git.refs.getHead();
			if (!head) {
				throw new Error('Could not determine current HEAD');
			}

			// Create backup branch
			await repository.git.branches.create(branchName, head.ref);
			Logger.log(`Created backup branch: ${branchName}`);
		} catch (error) {
			Logger.error(error, 'CommitComposerExecutor', 'createBackupBranch');
			throw new Error(`Failed to create backup branch: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async createBranch(
		repository: Repository,
		branch: DraftBranch,
		options?: ExecutionOptions,
	): Promise<void> {
		if (options?.dryRun) {
			Logger.log(`[DRY RUN] Would create branch: ${branch.name}`);
			return;
		}

		try {
			// Check if branch already exists
			const existingBranch = await repository.git.branches.get(branch.name);
			if (existingBranch) {
				throw new Error(`Branch ${branch.name} already exists`);
			}

			// Create the branch from the base commit
			await repository.git.branches.create(branch.name, branch.baseCommit);
			Logger.log(`Created branch: ${branch.name} from ${branch.baseCommit}`);
		} catch (error) {
			Logger.error(error, 'CommitComposerExecutor', 'createBranch');
			throw error;
		}
	}

	private async createCommit(
		repository: Repository,
		commit: DraftCommit,
		state: ComposerState,
		options?: ExecutionOptions,
	): Promise<string | undefined> {
		if (options?.dryRun) {
			Logger.log(`[DRY RUN] Would create commit: ${commit.message}`);
			return `dry-run-${commit.id}`;
		}

		try {
			// Find the target branch for this commit
			const targetBranch = state.branches.find(b => b.commitIds.includes(commit.id));
			
			// Switch to target branch if needed
			if (targetBranch && !targetBranch.isDefault) {
				await repository.git.checkout(targetBranch.name);
			}

			// Apply hunks to working directory
			await this.applyHunksToWorkingDirectory(repository, commit);

			// Stage the changes
			for (const file of commit.files) {
				await repository.git.add(file.uri.fsPath);
			}

			// Create the commit
			const commitSha = await repository.git.commit(commit.message, {
				description: commit.description,
				author: commit.author,
			});

			Logger.log(`Created commit: ${commitSha} - ${commit.message}`);
			return commitSha;
		} catch (error) {
			Logger.error(error, 'CommitComposerExecutor', 'createCommit');
			throw error;
		}
	}

	private async applyHunksToWorkingDirectory(repository: Repository, commit: DraftCommit): Promise<void> {
		// This is a simplified implementation
		// In a real implementation, this would:
		// 1. Parse each hunk's content
		// 2. Apply the changes to the actual files
		// 3. Handle conflicts and overlaps
		// 4. Validate that the changes can be applied

		for (const hunk of commit.hunks) {
			try {
				// For now, we'll assume the hunks are already applied to the working directory
				// In a real implementation, you would need to:
				// - Read the current file content
				// - Apply the hunk's changes
				// - Write the modified content back to the file
				Logger.log(`Applied hunk ${hunk.id} to ${hunk.fileUri}`);
			} catch (error) {
				Logger.error(error, 'CommitComposerExecutor', 'applyHunksToWorkingDirectory');
				throw new Error(`Failed to apply hunk ${hunk.id}: ${error instanceof Error ? error.message : String(error)}`);
			}
		}
	}

	/**
	 * Undo a composition execution by restoring from backup
	 */
	async undo(repository: Repository, backupBranchName: string): Promise<void> {
		try {
			// Switch to backup branch
			await repository.git.checkout(backupBranchName);
			
			// Delete the backup branch
			await repository.git.branches.delete(backupBranchName);
			
			Logger.log(`Restored from backup and cleaned up: ${backupBranchName}`);
		} catch (error) {
			Logger.error(error, 'CommitComposerExecutor', 'undo');
			throw new Error(`Failed to undo composition: ${error instanceof Error ? error.message : String(error)}`);
		}
	}
}
