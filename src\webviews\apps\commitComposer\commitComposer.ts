/*global document window*/
import './commitComposer.scss';
import type { IpcMessage } from '../../protocol';
import type { State } from '../../commitComposer/protocol';
import {
	InitializeCommand,
	UpdateCommitCommand,
	ReorderCommitsCommand,
	<PERSON><PERSON><PERSON>ommitCommand,
	DeleteCommit<PERSON>ommand,
	MoveHunkCommand,
	SelectCommitCommand,
	SelectBranchCommand,
	UndoCommand,
	RedoCommand,
	ExecuteCompositionCommand,
	CancelCompositionCommand,
	SaveDraftCommand,
	LoadDraftCommand,
	RegenerateCommitMessageRequest,
	ValidateCompositionRequest,
	PreviewExecutionRequest,
	DidChangeStateNotification,
	DidUpdateCommitNotification,
	DidUpdateBranchNotification,
	DidExecuteNotification,
	DidValidateNotification,
} from '../../commitComposer/protocol';
import { App } from '../shared/appBase';
import { DOM } from '../shared/dom';
import type { Disposable } from '../shared/events';

export class CommitComposerApp extends App<State> {
	private _selectedCommitId: string | undefined;
	private _selectedBranchId: string | undefined;
	private _isExecuting = false;

	constructor() {
		super('CommitComposer');
	}

	protected override onInitialize() {
		this.state = this.getState() ?? this.state;
		if (this.state != null) {
			this.refresh(this.state);
		}
	}

	protected override onBind(): Disposable[] {
		const disposables = super.onBind?.() ?? [];

		// Header actions
		disposables.push(
			DOM.on('#undo-btn', 'click', () => this.onUndo()),
			DOM.on('#redo-btn', 'click', () => this.onRedo()),
			DOM.on('#validate-btn', 'click', () => this.onValidate()),
			DOM.on('#preview-btn', 'click', () => this.onPreview()),
			DOM.on('#save-draft-btn', 'click', () => this.onSaveDraft()),
			DOM.on('#load-draft-btn', 'click', () => this.onLoadDraft())
		);

		// Commits panel actions
		disposables.push(
			DOM.on('#add-commit-btn', 'click', () => this.onAddCommit()),
			DOM.on('#regenerate-all-btn', 'click', () => this.onRegenerateAll()),
			DOM.on('#create-first-commit-btn', 'click', () => this.onAddCommit())
		);

		// Changes panel actions
		disposables.push(
			DOM.on('#toggle-file-tree-btn', 'click', () => this.onToggleFileTree()),
			DOM.on('#expand-all-btn', 'click', () => this.onExpandAll()),
			DOM.on('#collapse-all-btn', 'click', () => this.onCollapseAll())
		);

		// Branches panel actions
		disposables.push(
			DOM.on('#add-branch-btn', 'click', () => this.onAddBranch()),
			DOM.on('#toggle-branch-panel-btn', 'click', () => this.onToggleBranchPanel()),
			DOM.on('#create-first-branch-btn', 'click', () => this.onAddBranch())
		);

		// Footer actions
		disposables.push(
			DOM.on('#cancel-btn', 'click', () => this.onCancel()),
			DOM.on('#execute-btn', 'click', () => this.onExecute())
		);

		// Keyboard shortcuts
		disposables.push(
			DOM.on(window, 'keydown', e => {
				if (e.ctrlKey || e.metaKey) {
					switch (e.key) {
						case 'z':
							if (!e.shiftKey) {
								e.preventDefault();
								this.onUndo();
							}
							break;
						case 'y':
						case 'Z':
							if (e.key === 'y' || (e.key === 'Z' && e.shiftKey)) {
								e.preventDefault();
								this.onRedo();
							}
							break;
						case 's':
							e.preventDefault();
							this.onSaveDraft();
							break;
						case 'Enter':
							if (!this._isExecuting) {
								e.preventDefault();
								this.onExecute();
							}
							break;
					}
				} else if (e.key === 'Escape') {
					this.onCancel();
				}
			})
		);

		return disposables;
	}

	protected override onMessageReceived(msg: IpcMessage) {
		switch (true) {
			case DidChangeStateNotification.is(msg):
				this.onStateChanged(msg.params.state);
				break;
			case DidUpdateCommitNotification.is(msg):
				this.onCommitUpdated(msg.params.commit);
				break;
			case DidUpdateBranchNotification.is(msg):
				this.onBranchUpdated(msg.params.branch);
				break;
			case DidExecuteNotification.is(msg):
				this.onExecutionCompleted(msg.params.result);
				break;
			case DidValidateNotification.is(msg):
				this.onValidationCompleted(msg.params.errors, msg.params.warnings);
				break;
			default:
				super.onMessageReceived?.(msg);
				break;
		}
	}

	private refresh(state: State) {
		this.state = state;
		this.updateUI();
		this.renderCommits();
		this.renderChanges();
		this.renderBranches();
		this.updateStatus();
	}

	private updateUI() {
		if (!this.state) return;

		const { composerState, preferences, capabilities } = this.state;

		// Update source info
		const sourceInfo = document.getElementById('source-info');
		if (sourceInfo) {
			const sourceText = composerState.sourceType === 'workingChanges' 
				? 'Working Directory Changes'
				: `Commit Range: ${composerState.sourceRef?.ref ?? 'Unknown'} → ${composerState.targetRef?.ref ?? 'Unknown'}`;
			sourceInfo.textContent = sourceText;
		}

		// Update counts
		this.updateElement('#commit-count', composerState.commits.length.toString());
		this.updateElement('#branches-count', composerState.branches.length.toString());
		this.updateElement('#changes-count', composerState.unassignedHunks.length.toString());

		// Update button states
		this.updateButtonState('#undo-btn', !composerState.isExecuting);
		this.updateButtonState('#redo-btn', !composerState.isExecuting);
		this.updateButtonState('#execute-btn', 
			!composerState.isExecuting && 
			composerState.commits.length > 0 && 
			composerState.validationErrors.length === 0
		);

		// Show/hide panels based on preferences
		this.togglePanel('.commit-composer__branches-panel', preferences.showBranchPanel);
		
		// Update capabilities
		this.updateButtonState('#add-branch-btn', capabilities.canCreateBranches);
		this.updateButtonState('#regenerate-all-btn', capabilities.hasAI);
	}

	private renderCommits() {
		if (!this.state) return;

		const commitsList = document.getElementById('commits-list');
		const commitsEmpty = document.getElementById('commits-empty');
		
		if (!commitsList || !commitsEmpty) return;

		const commits = this.state.composerState.commits;

		if (commits.length === 0) {
			commitsList.style.display = 'none';
			commitsEmpty.style.display = 'block';
			return;
		}

		commitsList.style.display = 'block';
		commitsEmpty.style.display = 'none';

		// Clear existing commits
		commitsList.innerHTML = '';

		// Render each commit
		commits.forEach((commit, index) => {
			const commitElement = this.createCommitElement(commit, index);
			commitsList.appendChild(commitElement);
		});
	}

	private createCommitElement(commit: any, index: number): HTMLElement {
		const div = document.createElement('div');
		div.className = 'commit-composer__commit-item';
		div.dataset.commitId = commit.id;
		
		if (commit.id === this._selectedCommitId) {
			div.classList.add('commit-composer__commit-item--selected');
		}

		div.innerHTML = `
			<div class="commit-composer__commit-header">
				<div class="commit-composer__commit-handle" title="Drag to reorder">
					<code-icon icon="gripper"></code-icon>
				</div>
				<div class="commit-composer__commit-info">
					<div class="commit-composer__commit-message">
						<input 
							type="text" 
							value="${this.escapeHtml(commit.message)}" 
							placeholder="Commit message"
							class="commit-composer__commit-message-input"
						/>
					</div>
					<div class="commit-composer__commit-meta">
						<span class="commit-composer__commit-index">#${index + 1}</span>
						<span class="commit-composer__commit-files">${commit.files.length} files</span>
						<span class="commit-composer__commit-hunks">${commit.hunks.length} hunks</span>
						${commit.isGenerated ? '<span class="commit-composer__commit-ai-badge">AI</span>' : ''}
					</div>
				</div>
				<div class="commit-composer__commit-actions">
					<button class="commit-composer__commit-action" title="Regenerate message" data-action="regenerate">
						<code-icon icon="refresh"></code-icon>
					</button>
					<button class="commit-composer__commit-action" title="Split commit" data-action="split">
						<code-icon icon="split-horizontal"></code-icon>
					</button>
					<button class="commit-composer__commit-action" title="Delete commit" data-action="delete">
						<code-icon icon="trash"></code-icon>
					</button>
				</div>
			</div>
			${commit.description ? `
				<div class="commit-composer__commit-description">
					<textarea 
						placeholder="Commit description (optional)"
						class="commit-composer__commit-description-input"
					>${this.escapeHtml(commit.description)}</textarea>
				</div>
			` : ''}
		`;

		// Add event listeners
		this.bindCommitEvents(div, commit);

		return div;
	}

	private bindCommitEvents(element: HTMLElement, commit: any) {
		const commitId = commit.id;

		// Click to select
		DOM.on(element, 'click', (e) => {
			if ((e.target as HTMLElement).closest('.commit-composer__commit-action')) return;
			this.selectCommit(commitId);
		});

		// Message input
		const messageInput = element.querySelector('.commit-composer__commit-message-input') as HTMLInputElement;
		if (messageInput) {
			DOM.on(messageInput, 'blur', () => {
				this.updateCommitMessage(commitId, messageInput.value);
			});
			DOM.on(messageInput, 'keydown', (e) => {
				if (e.key === 'Enter') {
					messageInput.blur();
				}
			});
		}

		// Description input
		const descriptionInput = element.querySelector('.commit-composer__commit-description-input') as HTMLTextAreaElement;
		if (descriptionInput) {
			DOM.on(descriptionInput, 'blur', () => {
				this.updateCommitDescription(commitId, descriptionInput.value);
			});
		}

		// Action buttons
		DOM.on(element, 'click', (e) => {
			const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
			if (!action) return;

			e.stopPropagation();
			
			switch (action) {
				case 'regenerate':
					this.regenerateCommitMessage(commitId);
					break;
				case 'split':
					this.splitCommit(commitId);
					break;
				case 'delete':
					this.deleteCommit(commitId);
					break;
			}
		});
	}

	private renderChanges() {
		// TODO: Implement changes rendering
		const changesList = document.getElementById('changes-list');
		const changesEmpty = document.getElementById('changes-empty');
		
		if (!changesList || !changesEmpty) return;

		// For now, show empty state
		changesList.style.display = 'none';
		changesEmpty.style.display = 'block';
	}

	private renderBranches() {
		// TODO: Implement branches rendering
		const branchesList = document.getElementById('branches-list');
		const branchesEmpty = document.getElementById('branches-empty');
		
		if (!branchesList || !branchesEmpty) return;

		// For now, show empty state
		branchesList.style.display = 'none';
		branchesEmpty.style.display = 'block';
	}

	private updateStatus() {
		if (!this.state) return;

		const validationIcon = document.querySelector('.commit-composer__validation-icon');
		const validationText = document.getElementById('validation-text');
		const summaryText = document.getElementById('summary-text');

		if (validationIcon && validationText) {
			const hasErrors = this.state.composerState.validationErrors.length > 0;
			
			validationIcon.setAttribute('icon', hasErrors ? 'error' : 'check');
			validationText.textContent = hasErrors 
				? `${this.state.composerState.validationErrors.length} errors`
				: 'Ready to compose';
		}

		if (summaryText) {
			const commitCount = this.state.composerState.commits.length;
			const branchCount = this.state.composerState.branches.length;
			summaryText.textContent = `${commitCount} commits, ${branchCount} branches`;
		}
	}

	// Event handlers
	private onUndo() {
		this.sendCommand(UndoCommand, undefined);
	}

	private onRedo() {
		this.sendCommand(RedoCommand, undefined);
	}

	private onValidate() {
		this.sendRequest(ValidateCompositionRequest, {});
	}

	private onPreview() {
		this.sendRequest(PreviewExecutionRequest, {});
	}

	private onSaveDraft() {
		this.sendCommand(SaveDraftCommand, {});
	}

	private onLoadDraft() {
		this.sendCommand(LoadDraftCommand, { draftId: 'temp' });
	}

	private onAddCommit() {
		const message = prompt('Enter commit message:');
		if (message) {
			this.sendCommand(CreateCommitCommand, { message });
		}
	}

	private onRegenerateAll() {
		// TODO: Implement regenerate all
		console.log('Regenerate all commits');
	}

	private onToggleFileTree() {
		// TODO: Implement file tree toggle
		console.log('Toggle file tree');
	}

	private onExpandAll() {
		// TODO: Implement expand all
		console.log('Expand all');
	}

	private onCollapseAll() {
		// TODO: Implement collapse all
		console.log('Collapse all');
	}

	private onAddBranch() {
		const name = prompt('Enter branch name:');
		if (name) {
			// TODO: Implement branch creation
			console.log('Create branch:', name);
		}
	}

	private onToggleBranchPanel() {
		// TODO: Implement branch panel toggle
		console.log('Toggle branch panel');
	}

	private onCancel() {
		this.sendCommand(CancelCompositionCommand, undefined);
	}

	private onExecute() {
		if (this._isExecuting) return;
		
		this._isExecuting = true;
		this.updateButtonState('#execute-btn', false);
		this.showLoading('Executing composition...');
		
		this.sendCommand(ExecuteCompositionCommand, undefined);
	}

	// Commit operations
	private selectCommit(commitId: string) {
		this._selectedCommitId = commitId;
		this.sendCommand(SelectCommitCommand, { commitId });
		this.updateCommitSelection();
	}

	private updateCommitMessage(commitId: string, message: string) {
		this.sendCommand(UpdateCommitCommand, {
			commitId,
			changes: { message }
		});
	}

	private updateCommitDescription(commitId: string, description: string) {
		this.sendCommand(UpdateCommitCommand, {
			commitId,
			changes: { description }
		});
	}

	private regenerateCommitMessage(commitId: string) {
		this.sendRequest(RegenerateCommitMessageRequest, { commitId });
	}

	private splitCommit(commitId: string) {
		// TODO: Implement split commit
		console.log('Split commit:', commitId);
	}

	private deleteCommit(commitId: string) {
		if (confirm('Are you sure you want to delete this commit?')) {
			this.sendCommand(DeleteCommitCommand, { commitId });
		}
	}

	private updateCommitSelection() {
		document.querySelectorAll('.commit-composer__commit-item').forEach(el => {
			el.classList.toggle(
				'commit-composer__commit-item--selected',
				el.dataset.commitId === this._selectedCommitId
			);
		});
	}

	// State change handlers
	private onStateChanged(state: State) {
		this.refresh(state);
	}

	private onCommitUpdated(commit: any) {
		// TODO: Update specific commit in UI
		console.log('Commit updated:', commit);
	}

	private onBranchUpdated(branch: any) {
		// TODO: Update specific branch in UI
		console.log('Branch updated:', branch);
	}

	private onExecutionCompleted(result: any) {
		this._isExecuting = false;
		this.hideLoading();
		this.updateButtonState('#execute-btn', true);
		
		if (result.success) {
			this.showSuccess(`Successfully created ${result.createdCommits.length} commits!`);
		} else {
			this.showError(`Execution failed: ${result.errors.join(', ')}`);
		}
	}

	private onValidationCompleted(errors: string[], warnings: string[]) {
		console.log('Validation completed:', { errors, warnings });
		this.updateStatus();
	}

	// Utility methods
	private updateElement(selector: string, text: string) {
		const element = document.querySelector(selector);
		if (element) {
			element.textContent = text;
		}
	}

	private updateButtonState(selector: string, enabled: boolean) {
		const button = document.querySelector(selector) as HTMLButtonElement;
		if (button) {
			button.disabled = !enabled;
		}
	}

	private togglePanel(selector: string, visible: boolean) {
		const panel = document.querySelector(selector) as HTMLElement;
		if (panel) {
			panel.style.display = visible ? 'block' : 'none';
		}
	}

	private showLoading(message: string) {
		const overlay = document.getElementById('loading-overlay');
		const text = document.getElementById('loading-text');
		
		if (overlay && text) {
			text.textContent = message;
			overlay.style.display = 'flex';
		}
	}

	private hideLoading() {
		const overlay = document.getElementById('loading-overlay');
		if (overlay) {
			overlay.style.display = 'none';
		}
	}

	private showSuccess(message: string) {
		// TODO: Implement success notification
		console.log('Success:', message);
	}

	private showError(message: string) {
		// TODO: Implement error notification
		console.error('Error:', message);
	}

	private escapeHtml(text: string): string {
		const div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	}
}

new CommitComposerApp();
