/*global document window*/
import './commitComposer.scss';
import type { IpcMessage } from '../../protocol';
import type { State } from '../../commitComposer/protocol';
import {
	InitializeCommand,
	UpdateCommitCommand,
	ReorderCommitsCommand,
	<PERSON><PERSON><PERSON>ommitCommand,
	DeleteCommit<PERSON>ommand,
	MoveHunkCommand,
	SelectCommitCommand,
	SelectBranchCommand,
	UndoCommand,
	RedoCommand,
	ExecuteCompositionCommand,
	CancelCompositionCommand,
	SaveDraftCommand,
	LoadDraftCommand,
	RegenerateCommitMessageRequest,
	ValidateCompositionRequest,
	PreviewExecutionRequest,
	DidChangeStateNotification,
	DidUpdateCommitNotification,
	DidUpdateBranchNotification,
	DidExecuteNotification,
	DidValidateNotification,
} from '../../commitComposer/protocol';
import { App } from '../shared/appBase';
import { DOM } from '../shared/dom';
import type { Disposable } from '../shared/events';

export class CommitComposerApp extends App<State> {
	private _selectedCommitId: string | undefined;
	private _selectedBranchId: string | undefined;
	private _isExecuting = false;
	private _draggedElement: HTMLElement | undefined;
	private _draggedType: 'commit' | 'hunk' | undefined;
	private _draggedId: string | undefined;

	constructor() {
		super('CommitComposer');
	}

	protected override onInitialize() {
		this.state = this.getState() ?? this.state;
		if (this.state != null) {
			this.refresh(this.state);
		}
	}

	protected override onBind(): Disposable[] {
		const disposables = super.onBind?.() ?? [];

		// Header actions
		disposables.push(
			DOM.on('#undo-btn', 'click', () => this.onUndo()),
			DOM.on('#redo-btn', 'click', () => this.onRedo()),
			DOM.on('#validate-btn', 'click', () => this.onValidate()),
			DOM.on('#preview-btn', 'click', () => this.onPreview()),
			DOM.on('#save-draft-btn', 'click', () => this.onSaveDraft()),
			DOM.on('#load-draft-btn', 'click', () => this.onLoadDraft()),
		);

		// Commits panel actions
		disposables.push(
			DOM.on('#add-commit-btn', 'click', () => this.onAddCommit()),
			DOM.on('#regenerate-all-btn', 'click', () => this.onRegenerateAll()),
			DOM.on('#create-first-commit-btn', 'click', () => this.onAddCommit()),
		);

		// Changes panel actions
		disposables.push(
			DOM.on('#toggle-file-tree-btn', 'click', () => this.onToggleFileTree()),
			DOM.on('#expand-all-btn', 'click', () => this.onExpandAll()),
			DOM.on('#collapse-all-btn', 'click', () => this.onCollapseAll()),
		);

		// Branches panel actions
		disposables.push(
			DOM.on('#add-branch-btn', 'click', () => this.onAddBranch()),
			DOM.on('#toggle-branch-panel-btn', 'click', () => this.onToggleBranchPanel()),
			DOM.on('#create-first-branch-btn', 'click', () => this.onAddBranch()),
		);

		// Footer actions
		disposables.push(
			DOM.on('#cancel-btn', 'click', () => this.onCancel()),
			DOM.on('#execute-btn', 'click', () => this.onExecute()),
		);

		// Keyboard shortcuts
		disposables.push(
			DOM.on(window, 'keydown', e => {
				if (e.ctrlKey || e.metaKey) {
					switch (e.key) {
						case 'z':
							if (!e.shiftKey) {
								e.preventDefault();
								this.onUndo();
							}
							break;
						case 'y':
						case 'Z':
							if (e.key === 'y' || (e.key === 'Z' && e.shiftKey)) {
								e.preventDefault();
								this.onRedo();
							}
							break;
						case 's':
							e.preventDefault();
							this.onSaveDraft();
							break;
						case 'Enter':
							if (!this._isExecuting) {
								e.preventDefault();
								this.onExecute();
							}
							break;
					}
				} else if (e.key === 'Escape') {
					this.onCancel();
				}
			}),
		);

		return disposables;
	}

	protected override onMessageReceived(msg: IpcMessage) {
		switch (true) {
			case DidChangeStateNotification.is(msg):
				this.onStateChanged(msg.params.state);
				break;
			case DidUpdateCommitNotification.is(msg):
				this.onCommitUpdated(msg.params.commit);
				break;
			case DidUpdateBranchNotification.is(msg):
				this.onBranchUpdated(msg.params.branch);
				break;
			case DidExecuteNotification.is(msg):
				this.onExecutionCompleted(msg.params.result);
				break;
			case DidValidateNotification.is(msg):
				this.onValidationCompleted(msg.params.errors, msg.params.warnings);
				break;
			default:
				super.onMessageReceived?.(msg);
				break;
		}
	}

	private refresh(state: State) {
		this.state = state;
		this.updateUI();
		this.renderCommits();
		this.renderChanges();
		this.renderBranches();
		this.updateStatus();
	}

	private updateUI() {
		if (!this.state) return;

		const { composerState, preferences, capabilities } = this.state;

		// Update source info
		const sourceInfo = document.getElementById('source-info');
		if (sourceInfo) {
			const sourceText =
				composerState.sourceType === 'workingChanges'
					? 'Working Directory Changes'
					: `Commit Range: ${composerState.sourceRef?.ref ?? 'Unknown'} → ${composerState.targetRef?.ref ?? 'Unknown'}`;
			sourceInfo.textContent = sourceText;
		}

		// Update counts
		this.updateElement('#commit-count', composerState.commits.length.toString());
		this.updateElement('#branches-count', composerState.branches.length.toString());
		this.updateElement('#changes-count', composerState.unassignedHunks.length.toString());

		// Update button states
		this.updateButtonState('#undo-btn', !composerState.isExecuting);
		this.updateButtonState('#redo-btn', !composerState.isExecuting);
		this.updateButtonState(
			'#execute-btn',
			!composerState.isExecuting &&
				composerState.commits.length > 0 &&
				composerState.validationErrors.length === 0,
		);

		// Show/hide panels based on preferences
		this.togglePanel('.commit-composer__branches-panel', preferences.showBranchPanel);

		// Update capabilities
		this.updateButtonState('#add-branch-btn', capabilities.canCreateBranches);
		this.updateButtonState('#regenerate-all-btn', capabilities.hasAI);
	}

	private renderCommits() {
		if (!this.state) return;

		const commitsList = document.getElementById('commits-list');
		const commitsEmpty = document.getElementById('commits-empty');

		if (!commitsList || !commitsEmpty) return;

		const commits = this.state.composerState.commits;

		if (commits.length === 0) {
			commitsList.style.display = 'none';
			commitsEmpty.style.display = 'block';
			return;
		}

		commitsList.style.display = 'block';
		commitsEmpty.style.display = 'none';

		// Clear existing commits
		commitsList.innerHTML = '';

		// Render each commit
		commits.forEach((commit, index) => {
			const commitElement = this.createCommitElement(commit, index);
			commitsList.appendChild(commitElement);
		});
	}

	private createCommitElement(commit: any, index: number): HTMLElement {
		const div = document.createElement('div');
		div.className = 'commit-composer__commit-item';
		div.dataset.commitId = commit.id;
		div.draggable = true;

		if (commit.id === this._selectedCommitId) {
			div.classList.add('commit-composer__commit-item--selected');
		}

		div.innerHTML = `
			<div class="commit-composer__commit-header">
				<div class="commit-composer__commit-handle" title="Drag to reorder">
					<code-icon icon="gripper"></code-icon>
				</div>
				<div class="commit-composer__commit-info">
					<div class="commit-composer__commit-message">
						<input
							type="text"
							value="${this.escapeHtml(commit.message)}"
							placeholder="Commit message"
							class="commit-composer__commit-message-input"
						/>
					</div>
					<div class="commit-composer__commit-meta">
						<span class="commit-composer__commit-index">#${index + 1}</span>
						<span class="commit-composer__commit-files">${commit.files.length} files</span>
						<span class="commit-composer__commit-hunks">${commit.hunks.length} hunks</span>
						${commit.isGenerated ? '<span class="commit-composer__commit-ai-badge">AI</span>' : ''}
					</div>
				</div>
				<div class="commit-composer__commit-actions">
					<button class="commit-composer__commit-action" title="Regenerate message" data-action="regenerate">
						<code-icon icon="refresh"></code-icon>
					</button>
					<button class="commit-composer__commit-action" title="Split commit" data-action="split">
						<code-icon icon="split-horizontal"></code-icon>
					</button>
					<button class="commit-composer__commit-action" title="Delete commit" data-action="delete">
						<code-icon icon="trash"></code-icon>
					</button>
				</div>
			</div>
			${
				commit.description
					? `
				<div class="commit-composer__commit-description">
					<textarea
						placeholder="Commit description (optional)"
						class="commit-composer__commit-description-input"
					>${this.escapeHtml(commit.description)}</textarea>
				</div>
			`
					: ''
			}
		`;

		// Add hunks section
		const hunksContainer = document.createElement('div');
		hunksContainer.className = 'commit-composer__commit-hunks';
		commit.hunks.forEach((hunk: any, hunkIndex: number) => {
			const hunkElement = this.createHunkElement(hunk, hunkIndex);
			hunksContainer.appendChild(hunkElement);
		});
		div.appendChild(hunksContainer);

		// Add event listeners
		this.bindCommitEvents(div, commit);
		this.bindDragEvents(div, 'commit', commit.id);

		return div;
	}

	private createHunkElement(hunk: any, index: number): HTMLElement {
		const div = document.createElement('div');
		div.className = 'commit-composer__hunk-item';
		div.dataset.hunkId = hunk.id;
		div.draggable = true;

		div.innerHTML = `
			<div class="commit-composer__hunk-header">
				<div class="commit-composer__hunk-handle" title="Drag to move">
					<code-icon icon="gripper"></code-icon>
				</div>
				<div class="commit-composer__hunk-info">
					<span class="commit-composer__hunk-file">${this.getFileName(hunk.fileUri)}</span>
					<span class="commit-composer__hunk-range">@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@</span>
				</div>
				<div class="commit-composer__hunk-actions">
					<button class="commit-composer__hunk-action" title="View diff" data-action="view">
						<code-icon icon="eye"></code-icon>
					</button>
					<button class="commit-composer__hunk-action" title="Remove from commit" data-action="remove">
						<code-icon icon="close"></code-icon>
					</button>
				</div>
			</div>
		`;

		// Add drag events
		this.bindDragEvents(div, 'hunk', hunk.id);

		// Add hunk-specific events
		DOM.on(div, 'click', e => {
			const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
			if (!action) return;

			e.stopPropagation();

			switch (action) {
				case 'view':
					this.viewHunkDiff(hunk.id);
					break;
				case 'remove':
					this.removeHunkFromCommit(hunk.id);
					break;
			}
		});

		return div;
	}

	private createFileElement(fileUri: string, hunks: any[]): HTMLElement {
		const div = document.createElement('div');
		div.className = 'commit-composer__file-item';
		div.dataset.fileUri = fileUri;

		const fileName = this.getFileName(fileUri);
		const isExpanded = true; // TODO: Track expansion state

		div.innerHTML = `
			<div class="commit-composer__file-header">
				<button class="commit-composer__file-toggle" title="Toggle file">
					<code-icon icon="${isExpanded ? 'chevron-down' : 'chevron-right'}"></code-icon>
				</button>
				<div class="commit-composer__file-info">
					<code-icon icon="file"></code-icon>
					<span class="commit-composer__file-name">${this.escapeHtml(fileName)}</span>
					<span class="commit-composer__file-path">${this.escapeHtml(fileUri)}</span>
				</div>
				<div class="commit-composer__file-stats">
					<span class="commit-composer__hunk-count">${hunks.length} hunks</span>
				</div>
			</div>
			<div class="commit-composer__file-hunks" style="display: ${isExpanded ? 'block' : 'none'}">
				<!-- Hunks will be added here -->
			</div>
		`;

		// Add hunks
		const hunksContainer = div.querySelector('.commit-composer__file-hunks') as HTMLElement;
		hunks.forEach((hunk, index) => {
			const hunkElement = this.createHunkElement(hunk, index);
			hunksContainer.appendChild(hunkElement);
		});

		// Add toggle functionality
		const toggleButton = div.querySelector('.commit-composer__file-toggle') as HTMLButtonElement;
		DOM.on(toggleButton, 'click', () => {
			const hunksContainer = div.querySelector('.commit-composer__file-hunks') as HTMLElement;
			const icon = toggleButton.querySelector('code-icon') as HTMLElement;
			const isCurrentlyExpanded = hunksContainer.style.display !== 'none';

			hunksContainer.style.display = isCurrentlyExpanded ? 'none' : 'block';
			icon.setAttribute('icon', isCurrentlyExpanded ? 'chevron-right' : 'chevron-down');
		});

		return div;
	}

	private createBranchElement(branch: any): HTMLElement {
		const div = document.createElement('div');
		div.className = 'commit-composer__branch-item';
		div.dataset.branchId = branch.id;

		if (branch.id === this._selectedBranchId) {
			div.classList.add('commit-composer__branch-item--selected');
		}

		const commitCount = branch.commitIds.length;
		const isDefault = branch.isDefault;

		div.innerHTML = `
			<div class="commit-composer__branch-header">
				<div class="commit-composer__branch-info">
					<code-icon icon="git-branch" style="color: ${branch.color || '#007acc'}"></code-icon>
					<input
						type="text"
						value="${this.escapeHtml(branch.name)}"
						placeholder="Branch name"
						class="commit-composer__branch-name-input"
						${isDefault ? 'readonly' : ''}
					/>
				</div>
				<div class="commit-composer__branch-stats">
					<span class="commit-composer__commit-count">${commitCount} commits</span>
				</div>
				${
					!isDefault
						? `
					<div class="commit-composer__branch-actions">
						<button class="commit-composer__branch-action" title="Delete branch" data-action="delete">
							<code-icon icon="trash"></code-icon>
						</button>
					</div>
				`
						: ''
				}
			</div>
			${
				branch.description
					? `
				<div class="commit-composer__branch-description">
					<textarea
						placeholder="Branch description (optional)"
						class="commit-composer__branch-description-input"
					>${this.escapeHtml(branch.description)}</textarea>
				</div>
			`
					: ''
			}
		`;

		// Add event listeners
		this.bindBranchEvents(div, branch);

		return div;
	}

	private bindCommitEvents(element: HTMLElement, commit: any) {
		const commitId = commit.id;

		// Click to select
		DOM.on(element, 'click', e => {
			if ((e.target as HTMLElement).closest('.commit-composer__commit-action')) return;
			this.selectCommit(commitId);
		});

		// Message input
		const messageInput = element.querySelector('.commit-composer__commit-message-input') as HTMLInputElement;
		if (messageInput) {
			DOM.on(messageInput, 'blur', () => {
				this.updateCommitMessage(commitId, messageInput.value);
			});
			DOM.on(messageInput, 'keydown', e => {
				if (e.key === 'Enter') {
					messageInput.blur();
				}
			});
		}

		// Description input
		const descriptionInput = element.querySelector(
			'.commit-composer__commit-description-input',
		) as HTMLTextAreaElement;
		if (descriptionInput) {
			DOM.on(descriptionInput, 'blur', () => {
				this.updateCommitDescription(commitId, descriptionInput.value);
			});
		}

		// Action buttons
		DOM.on(element, 'click', e => {
			const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
			if (!action) return;

			e.stopPropagation();

			switch (action) {
				case 'regenerate':
					this.regenerateCommitMessage(commitId);
					break;
				case 'split':
					this.splitCommit(commitId);
					break;
				case 'delete':
					this.deleteCommit(commitId);
					break;
			}
		});
	}

	private bindBranchEvents(element: HTMLElement, branch: any) {
		const branchId = branch.id;

		// Click to select
		DOM.on(element, 'click', e => {
			if ((e.target as HTMLElement).closest('.commit-composer__branch-action')) return;
			this.selectBranch(branchId);
		});

		// Branch name input
		const nameInput = element.querySelector('.commit-composer__branch-name-input') as HTMLInputElement;
		if (nameInput && !branch.isDefault) {
			DOM.on(nameInput, 'blur', () => {
				this.updateBranchName(branchId, nameInput.value);
			});
			DOM.on(nameInput, 'keydown', e => {
				if (e.key === 'Enter') {
					nameInput.blur();
				}
			});
		}

		// Description input
		const descriptionInput = element.querySelector(
			'.commit-composer__branch-description-input',
		) as HTMLTextAreaElement;
		if (descriptionInput) {
			DOM.on(descriptionInput, 'blur', () => {
				this.updateBranchDescription(branchId, descriptionInput.value);
			});
		}

		// Action buttons
		DOM.on(element, 'click', e => {
			const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
			if (!action) return;

			e.stopPropagation();

			switch (action) {
				case 'delete':
					this.deleteBranch(branchId);
					break;
			}
		});
	}

	private renderChanges() {
		if (!this.state) return;

		const changesList = document.getElementById('changes-list');
		const changesEmpty = document.getElementById('changes-empty');

		if (!changesList || !changesEmpty) return;

		const unassignedHunks = this.state.composerState.unassignedHunks;

		if (unassignedHunks.length === 0) {
			changesList.style.display = 'none';
			changesEmpty.style.display = 'block';
			return;
		}

		changesList.style.display = 'block';
		changesEmpty.style.display = 'none';

		// Clear existing content
		changesList.innerHTML = '';

		// Group hunks by file
		const fileGroups = new Map<string, any[]>();
		unassignedHunks.forEach(hunk => {
			if (!fileGroups.has(hunk.fileUri)) {
				fileGroups.set(hunk.fileUri, []);
			}
			fileGroups.get(hunk.fileUri)!.push(hunk);
		});

		// Render file groups
		fileGroups.forEach((hunks, fileUri) => {
			const fileElement = this.createFileElement(fileUri, hunks);
			changesList.appendChild(fileElement);
		});
	}

	private renderBranches() {
		if (!this.state) return;

		const branchesList = document.getElementById('branches-list');
		const branchesEmpty = document.getElementById('branches-empty');

		if (!branchesList || !branchesEmpty) return;

		const branches = this.state.composerState.branches;

		if (branches.length === 0) {
			branchesList.style.display = 'none';
			branchesEmpty.style.display = 'block';
			return;
		}

		branchesList.style.display = 'block';
		branchesEmpty.style.display = 'none';

		// Clear existing branches
		branchesList.innerHTML = '';

		// Render each branch
		branches.forEach(branch => {
			const branchElement = this.createBranchElement(branch);
			branchesList.appendChild(branchElement);
		});
	}

	private updateStatus() {
		if (!this.state) return;

		const validationIcon = document.querySelector('.commit-composer__validation-icon');
		const validationText = document.getElementById('validation-text');
		const summaryText = document.getElementById('summary-text');

		if (validationIcon && validationText) {
			const hasErrors = this.state.composerState.validationErrors?.length > 0;
			const hasWarnings = this.state.composerState.validationWarnings?.length > 0;

			if (hasErrors) {
				validationIcon.setAttribute('icon', 'error');
				validationText.textContent = `${this.state.composerState.validationErrors.length} error${this.state.composerState.validationErrors.length > 1 ? 's' : ''}`;
				validationText.className = 'commit-composer__validation-text--error';
			} else if (hasWarnings) {
				validationIcon.setAttribute('icon', 'warning');
				validationText.textContent = `${this.state.composerState.validationWarnings.length} warning${this.state.composerState.validationWarnings.length > 1 ? 's' : ''}`;
				validationText.className = 'commit-composer__validation-text--warning';
			} else {
				validationIcon.setAttribute('icon', 'check');
				validationText.textContent = 'Ready to compose';
				validationText.className = 'commit-composer__validation-text--success';
			}
		}

		if (summaryText) {
			const commitCount = this.state.composerState.commits.length;
			const branchCount = this.state.composerState.branches.length;
			const hunkCount = this.state.composerState.unassignedHunks.length;
			summaryText.textContent = `${commitCount} commits, ${branchCount} branches, ${hunkCount} unassigned hunks`;
		}

		// Update validation details if they exist
		this.updateValidationDetails();
	}

	private updateValidationDetails() {
		if (!this.state) return;

		// Update validation panel if it exists
		const validationPanel = document.getElementById('validation-panel');
		if (!validationPanel) return;

		const errors = this.state.composerState.validationErrors || [];
		const warnings = this.state.composerState.validationWarnings || [];
		const conflicts = this.state.composerState.conflicts || [];

		if (errors.length === 0 && warnings.length === 0 && conflicts.length === 0) {
			validationPanel.style.display = 'none';
			return;
		}

		validationPanel.style.display = 'block';
		validationPanel.innerHTML = `
			<div class="commit-composer__validation-header">
				<h3>Validation Results</h3>
				<button class="commit-composer__validation-close" title="Close">
					<code-icon icon="close"></code-icon>
				</button>
			</div>
			<div class="commit-composer__validation-content">
				${errors.length > 0 ? this.renderValidationSection('Errors', errors, 'error') : ''}
				${warnings.length > 0 ? this.renderValidationSection('Warnings', warnings, 'warning') : ''}
				${conflicts.length > 0 ? this.renderValidationSection('Conflicts', conflicts, 'conflict') : ''}
			</div>
		`;

		// Add close functionality
		const closeButton = validationPanel.querySelector('.commit-composer__validation-close');
		if (closeButton) {
			DOM.on(closeButton, 'click', () => {
				validationPanel.style.display = 'none';
			});
		}
	}

	private renderValidationSection(title: string, items: any[], type: 'error' | 'warning' | 'conflict'): string {
		const iconMap = {
			error: 'error',
			warning: 'warning',
			conflict: 'issues',
		};

		return `
			<div class="commit-composer__validation-section commit-composer__validation-section--${type}">
				<div class="commit-composer__validation-section-header">
					<code-icon icon="${iconMap[type]}"></code-icon>
					<h4>${title} (${items.length})</h4>
				</div>
				<div class="commit-composer__validation-items">
					${items.map(item => this.renderValidationItem(item, type)).join('')}
				</div>
			</div>
		`;
	}

	private renderValidationItem(item: any, type: 'error' | 'warning' | 'conflict'): string {
		const message = typeof item === 'string' ? item : item.message;
		const context = typeof item === 'object' ? item.context : undefined;
		const suggestedResolution = typeof item === 'object' ? item.suggestedResolution : undefined;

		return `
			<div class="commit-composer__validation-item">
				<div class="commit-composer__validation-message">${this.escapeHtml(message)}</div>
				${
					context
						? `
					<div class="commit-composer__validation-context">
						${context.commitId ? `<span class="commit-composer__validation-tag">Commit: ${context.commitId}</span>` : ''}
						${context.branchId ? `<span class="commit-composer__validation-tag">Branch: ${context.branchId}</span>` : ''}
						${context.fileUri ? `<span class="commit-composer__validation-tag">File: ${this.getFileName(context.fileUri)}</span>` : ''}
					</div>
				`
						: ''
				}
				${
					suggestedResolution
						? `
					<div class="commit-composer__validation-resolution">
						<strong>Suggestion:</strong> ${this.escapeHtml(suggestedResolution)}
					</div>
				`
						: ''
				}
			</div>
		`;
	}

	// Event handlers
	private onUndo() {
		this.sendCommand(UndoCommand, undefined);
	}

	private onRedo() {
		this.sendCommand(RedoCommand, undefined);
	}

	private onValidate() {
		this.sendRequest(ValidateCompositionRequest, {});
	}

	private onPreview() {
		this.sendRequest(PreviewExecutionRequest, {});
	}

	private onSaveDraft() {
		this.sendCommand(SaveDraftCommand, {});
	}

	private onLoadDraft() {
		this.sendCommand(LoadDraftCommand, { draftId: 'temp' });
	}

	private onAddCommit() {
		const message = prompt('Enter commit message:');
		if (message) {
			this.sendCommand(CreateCommitCommand, { message });
		}
	}

	private onRegenerateAll() {
		// TODO: Implement regenerate all
		console.log('Regenerate all commits');
	}

	private onToggleFileTree() {
		// TODO: Implement file tree toggle
		console.log('Toggle file tree');
	}

	private onExpandAll() {
		// TODO: Implement expand all
		console.log('Expand all');
	}

	private onCollapseAll() {
		// TODO: Implement collapse all
		console.log('Collapse all');
	}

	private onAddBranch() {
		const name = prompt('Enter branch name:');
		if (name) {
			// TODO: Implement branch creation
			console.log('Create branch:', name);
		}
	}

	private onToggleBranchPanel() {
		// TODO: Implement branch panel toggle
		console.log('Toggle branch panel');
	}

	private onCancel() {
		this.sendCommand(CancelCompositionCommand, undefined);
	}

	private async onExecute() {
		if (this._isExecuting) return;

		// First, show execution preview
		const shouldProceed = await this.showExecutionPreview();
		if (!shouldProceed) return;

		this._isExecuting = true;
		this.updateButtonState('#execute-btn', false);
		this.showLoading('Executing composition...');

		this.sendCommand(ExecuteCompositionCommand, undefined);
	}

	private async showExecutionPreview(): Promise<boolean> {
		// Get execution preview from backend
		try {
			const preview = await this.sendRequest(PreviewExecutionRequest, {});

			if (!preview.success) {
				this.showExecutionError('Cannot execute composition', preview.errors || ['Unknown error']);
				return false;
			}

			// Show preview modal
			return this.showExecutionModal(preview);
		} catch (error) {
			this.showExecutionError('Failed to get execution preview', [String(error)]);
			return false;
		}
	}

	private showExecutionModal(preview: any): Promise<boolean> {
		return new Promise(resolve => {
			// Create modal overlay
			const overlay = document.createElement('div');
			overlay.className = 'commit-composer__modal-overlay';

			overlay.innerHTML = `
				<div class="commit-composer__modal">
					<div class="commit-composer__modal-header">
						<h2>Execution Preview</h2>
						<button class="commit-composer__modal-close" title="Close">
							<code-icon icon="close"></code-icon>
						</button>
					</div>
					<div class="commit-composer__modal-content">
						${this.renderExecutionPreview(preview)}
					</div>
					<div class="commit-composer__modal-actions">
						<button class="commit-composer__button commit-composer__button--secondary" data-action="cancel">
							Cancel
						</button>
						<button class="commit-composer__button commit-composer__button--primary" data-action="execute">
							Execute Composition
						</button>
					</div>
				</div>
			`;

			// Add event listeners
			DOM.on(overlay, 'click', e => {
				const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
				const closeButton = (e.target as HTMLElement).closest('.commit-composer__modal-close');

				if (action === 'execute') {
					document.body.removeChild(overlay);
					resolve(true);
				} else if (action === 'cancel' || closeButton || e.target === overlay) {
					document.body.removeChild(overlay);
					resolve(false);
				}
			});

			document.body.appendChild(overlay);
		});
	}

	private renderExecutionPreview(preview: any): string {
		const plan = preview.executionPlan;
		const validation = preview.validation;

		return `
			<div class="commit-composer__execution-preview">
				<div class="commit-composer__execution-summary">
					<h3>Execution Summary</h3>
					<div class="commit-composer__execution-stats">
						<div class="commit-composer__execution-stat">
							<code-icon icon="git-commit"></code-icon>
							<span>${plan.steps.filter((s: any) => s.type === 'commit').length} commits to create</span>
						</div>
						<div class="commit-composer__execution-stat">
							<code-icon icon="git-branch"></code-icon>
							<span>${plan.steps.filter((s: any) => s.type === 'branch').length} branches to create</span>
						</div>
						<div class="commit-composer__execution-stat">
							<code-icon icon="clock"></code-icon>
							<span>~${Math.ceil(plan.estimatedDuration / 1000)}s estimated</span>
						</div>
					</div>
				</div>

				${
					plan.risksAssessment.length > 0
						? `
					<div class="commit-composer__execution-risks">
						<h4><code-icon icon="warning"></code-icon> Risks & Considerations</h4>
						<ul>
							${plan.risksAssessment.map((risk: string) => `<li>${this.escapeHtml(risk)}</li>`).join('')}
						</ul>
					</div>
				`
						: ''
				}

				<div class="commit-composer__execution-steps">
					<h4>Execution Steps</h4>
					<ol>
						${plan.steps
							.map(
								(step: any) => `
							<li class="commit-composer__execution-step">
								<code-icon icon="${this.getStepIcon(step.type)}"></code-icon>
								${this.escapeHtml(step.description)}
							</li>
						`,
							)
							.join('')}
					</ol>
				</div>

				${
					validation.warnings.length > 0
						? `
					<div class="commit-composer__execution-warnings">
						<h4><code-icon icon="warning"></code-icon> Warnings</h4>
						<ul>
							${validation.warnings.map((warning: string) => `<li>${this.escapeHtml(warning)}</li>`).join('')}
						</ul>
					</div>
				`
						: ''
				}
			</div>
		`;
	}

	private getStepIcon(stepType: string): string {
		const iconMap: Record<string, string> = {
			backup: 'archive',
			branch: 'git-branch',
			commit: 'git-commit',
			cleanup: 'trash',
		};
		return iconMap[stepType] || 'circle';
	}

	private showExecutionError(title: string, errors: string[]) {
		// Create error modal
		const overlay = document.createElement('div');
		overlay.className = 'commit-composer__modal-overlay';

		overlay.innerHTML = `
			<div class="commit-composer__modal commit-composer__modal--error">
				<div class="commit-composer__modal-header">
					<h2><code-icon icon="error"></code-icon> ${this.escapeHtml(title)}</h2>
					<button class="commit-composer__modal-close" title="Close">
						<code-icon icon="close"></code-icon>
					</button>
				</div>
				<div class="commit-composer__modal-content">
					<div class="commit-composer__error-list">
						${errors
							.map(
								error => `
							<div class="commit-composer__error-item">
								<code-icon icon="error"></code-icon>
								${this.escapeHtml(error)}
							</div>
						`,
							)
							.join('')}
					</div>
				</div>
				<div class="commit-composer__modal-actions">
					<button class="commit-composer__button commit-composer__button--primary" data-action="close">
						Close
					</button>
				</div>
			</div>
		`;

		// Add event listeners
		DOM.on(overlay, 'click', e => {
			const action = (e.target as HTMLElement).closest('[data-action]')?.getAttribute('data-action');
			const closeButton = (e.target as HTMLElement).closest('.commit-composer__modal-close');

			if (action === 'close' || closeButton || e.target === overlay) {
				document.body.removeChild(overlay);
			}
		});

		document.body.appendChild(overlay);
	}

	// Commit operations
	private selectCommit(commitId: string) {
		this._selectedCommitId = commitId;
		this.sendCommand(SelectCommitCommand, { commitId });
		this.updateCommitSelection();
	}

	private updateCommitMessage(commitId: string, message: string) {
		this.sendCommand(UpdateCommitCommand, {
			commitId,
			changes: { message },
		});
	}

	private updateCommitDescription(commitId: string, description: string) {
		this.sendCommand(UpdateCommitCommand, {
			commitId,
			changes: { description },
		});
	}

	private regenerateCommitMessage(commitId: string) {
		this.sendRequest(RegenerateCommitMessageRequest, { commitId });
	}

	private splitCommit(commitId: string) {
		// TODO: Implement split commit
		console.log('Split commit:', commitId);
	}

	private deleteCommit(commitId: string) {
		if (confirm('Are you sure you want to delete this commit?')) {
			this.sendCommand(DeleteCommitCommand, { commitId });
		}
	}

	// Branch operations
	private selectBranch(branchId: string) {
		this._selectedBranchId = branchId;
		this.sendCommand(SelectBranchCommand, { branchId });
		this.updateBranchSelection();
	}

	private updateBranchName(branchId: string, name: string) {
		this.sendCommand(UpdateBranchCommand, {
			branchId,
			changes: { name },
		});
	}

	private updateBranchDescription(branchId: string, description: string) {
		this.sendCommand(UpdateBranchCommand, {
			branchId,
			changes: { description },
		});
	}

	private deleteBranch(branchId: string) {
		if (confirm('Are you sure you want to delete this branch?')) {
			this.sendCommand(DeleteBranchCommand, { branchId });
		}
	}

	private updateBranchSelection() {
		document.querySelectorAll('.commit-composer__branch-item').forEach(el => {
			el.classList.toggle(
				'commit-composer__branch-item--selected',
				el.dataset.branchId === this._selectedBranchId,
			);
		});
	}

	private updateCommitSelection() {
		document.querySelectorAll('.commit-composer__commit-item').forEach(el => {
			el.classList.toggle(
				'commit-composer__commit-item--selected',
				el.dataset.commitId === this._selectedCommitId,
			);
		});
	}

	// State change handlers
	private onStateChanged(state: State) {
		this.refresh(state);
	}

	private onCommitUpdated(commit: any) {
		// TODO: Update specific commit in UI
		console.log('Commit updated:', commit);
	}

	private onBranchUpdated(branch: any) {
		// TODO: Update specific branch in UI
		console.log('Branch updated:', branch);
	}

	private onExecutionCompleted(result: any) {
		this._isExecuting = false;
		this.hideLoading();
		this.updateButtonState('#execute-btn', true);

		if (result.success) {
			this.showSuccess(`Successfully created ${result.createdCommits.length} commits!`);
		} else {
			this.showError(`Execution failed: ${result.errors.join(', ')}`);
		}
	}

	private onValidationCompleted(errors: string[], warnings: string[]) {
		console.log('Validation completed:', { errors, warnings });
		this.updateStatus();
	}

	// Utility methods
	private updateElement(selector: string, text: string) {
		const element = document.querySelector(selector);
		if (element) {
			element.textContent = text;
		}
	}

	private updateButtonState(selector: string, enabled: boolean) {
		const button = document.querySelector(selector) as HTMLButtonElement;
		if (button) {
			button.disabled = !enabled;
		}
	}

	private togglePanel(selector: string, visible: boolean) {
		const panel = document.querySelector(selector) as HTMLElement;
		if (panel) {
			panel.style.display = visible ? 'block' : 'none';
		}
	}

	private showLoading(message: string) {
		const overlay = document.getElementById('loading-overlay');
		const text = document.getElementById('loading-text');

		if (overlay && text) {
			text.textContent = message;
			overlay.style.display = 'flex';
		}
	}

	private hideLoading() {
		const overlay = document.getElementById('loading-overlay');
		if (overlay) {
			overlay.style.display = 'none';
		}
	}

	private showSuccess(message: string) {
		// TODO: Implement success notification
		console.log('Success:', message);
	}

	private showError(message: string) {
		// TODO: Implement error notification
		console.error('Error:', message);
	}

	private escapeHtml(text: string): string {
		const div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	}

	// Drag and drop functionality
	private bindDragEvents(element: HTMLElement, type: 'commit' | 'hunk', id: string) {
		DOM.on(element, 'dragstart', e => {
			this._draggedElement = element;
			this._draggedType = type;
			this._draggedId = id;

			element.classList.add('commit-composer__dragging');

			if (e.dataTransfer) {
				e.dataTransfer.effectAllowed = 'move';
				e.dataTransfer.setData('text/plain', `${type}:${id}`);
			}
		});

		DOM.on(element, 'dragend', () => {
			element.classList.remove('commit-composer__dragging');
			this.clearDragState();
		});

		DOM.on(element, 'dragover', e => {
			e.preventDefault();
			if (e.dataTransfer) {
				e.dataTransfer.dropEffect = 'move';
			}

			if (this._draggedType === type) {
				element.classList.add('commit-composer__drop-target');
			}
		});

		DOM.on(element, 'dragleave', () => {
			element.classList.remove('commit-composer__drop-target');
		});

		DOM.on(element, 'drop', e => {
			e.preventDefault();
			element.classList.remove('commit-composer__drop-target');

			if (!this._draggedId || !this._draggedType) return;

			if (this._draggedType === 'commit' && type === 'commit') {
				this.handleCommitReorder(this._draggedId, id);
			} else if (this._draggedType === 'hunk' && type === 'commit') {
				this.handleHunkMove(this._draggedId, id);
			}

			this.clearDragState();
		});
	}

	private clearDragState() {
		this._draggedElement = undefined;
		this._draggedType = undefined;
		this._draggedId = undefined;

		// Remove all drag-related classes
		document.querySelectorAll('.commit-composer__dragging, .commit-composer__drop-target').forEach(el => {
			el.classList.remove('commit-composer__dragging', 'commit-composer__drop-target');
		});
	}

	private handleCommitReorder(draggedCommitId: string, targetCommitId: string) {
		if (draggedCommitId === targetCommitId) return;

		if (!this.state?.composerState.commits) return;

		const commits = this.state.composerState.commits;
		const draggedIndex = commits.findIndex(c => c.id === draggedCommitId);
		const targetIndex = commits.findIndex(c => c.id === targetCommitId);

		if (draggedIndex === -1 || targetIndex === -1) return;

		// Create new order
		const newOrder = [...commits];
		const [draggedCommit] = newOrder.splice(draggedIndex, 1);
		newOrder.splice(targetIndex, 0, draggedCommit);

		// Send reorder command
		this.sendCommand(ReorderCommitsCommand, {
			commitIds: newOrder.map(c => c.id),
		});
	}

	private handleHunkMove(hunkId: string, targetCommitId: string) {
		this.sendCommand(MoveHunkCommand, {
			hunkId,
			targetCommitId,
		});
	}

	// Utility methods for hunks
	private getFileName(fileUri: string): string {
		return fileUri.split('/').pop() || fileUri;
	}

	private viewHunkDiff(hunkId: string) {
		// TODO: Implement hunk diff viewer
		console.log('View hunk diff:', hunkId);
	}

	private removeHunkFromCommit(hunkId: string) {
		// Move hunk to unassigned
		this.sendCommand(MoveHunkCommand, {
			hunkId,
			targetCommitId: '', // Empty string means unassigned
		});
	}
}

new CommitComposerApp();
