import type { Disposable, Webview } from 'vscode';
import { window } from 'vscode';
import type { Container } from '../../container';
import type { ComposerState } from '../../models/commitComposer/composerState';
import { Logger } from '../../system/logger';
import type { Serialized } from '../../system/serialize';
import type { IpcMessage } from '../protocol';
import type { WebviewHost, WebviewProvider, WebviewShowingArgs } from '../webviewProvider';
import type { WebviewShowOptions } from '../webviewsController';
import { isSerializedState } from '../webviewsController';
import type {
	State,
	InitializeParams,
	UpdateCommitParams,
	ReorderCommitsParams,
	CreateCommitParams,
	DeleteCommitParams,
	SquashCommitsParams,
	SplitCommitParams,
	MoveHunkParams,
	CreateBranchParams,
	UpdateBranchParams,
	DeleteBranchParams,
	MoveCommitToBranchParams,
	SelectCommitParams,
	SelectBranchParams,
	SaveDraftParams,
	LoadDraftParams,
	RegenerateCommitMessageParams,
	GetFileContentParams,
	ValidateCompositionParams,
	PreviewExecutionParams,
} from './protocol';
import {
	InitializeCommand,
	UpdateCommitCommand,
	ReorderCommitsCommand,
	CreateCommitCommand,
	DeleteCommitCommand,
	SquashCommitsCommand,
	SplitCommitCommand,
	MoveHunkCommand,
	CreateBranchCommand,
	UpdateBranchCommand,
	DeleteBranchCommand,
	MoveCommitToBranchCommand,
	SelectCommitCommand,
	SelectBranchCommand,
	UndoCommand,
	RedoCommand,
	ExecuteCompositionCommand,
	CancelCompositionCommand,
	SaveDraftCommand,
	LoadDraftCommand,
	RegenerateCommitMessageRequest,
	GetFileContentRequest,
	ValidateCompositionRequest,
	PreviewExecutionRequest,
	DidChangeStateNotification,
	DidUpdateCommitNotification,
	DidUpdateBranchNotification,
	DidExecuteNotification,
	DidValidateNotification,
} from './protocol';

export interface CommitComposerWebviewShowingArgs extends WebviewShowingArgs {
	readonly sessionId: string;
	readonly sourceType: 'workingChanges' | 'commitRange';
	readonly sourceRef?: string;
	readonly targetRef?: string;
}

export class CommitComposerWebviewProvider
	implements WebviewProvider<State, Serialized<State>, CommitComposerWebviewShowingArgs>
{
	private readonly _disposable: Disposable;
	private _sessionId: string | undefined;
	private _state: ComposerState | undefined;

	constructor(
		private readonly container: Container,
		private readonly host: WebviewHost<'gitlens.views.commitComposer'>,
	) {
		this._disposable = Disposable
			.from
			// Listen for composer service events if needed
			();
	}

	dispose(): void {
		this._disposable.dispose();
		if (this._sessionId) {
			this.container.commitComposer.closeSession(this._sessionId);
		}
	}

	getSplitArgs(): Record<string, unknown> {
		return {
			sessionId: this._sessionId,
		};
	}

	async onShowing(
		loading: boolean,
		options?: WebviewShowOptions,
		...args: CommitComposerWebviewShowingArgs[]
	): Promise<boolean> {
		const [{ sessionId, sourceType, sourceRef, targetRef }] = args;

		if (loading && this._sessionId === sessionId) {
			return true;
		}

		this._sessionId = sessionId;
		this._state = this.container.commitComposer.getSession(sessionId);

		if (!this._state) {
			void window.showErrorMessage('Commit composer session not found');
			return false;
		}

		return true;
	}

	includeBootstrap(): Promise<Serialized<State>> {
		return this.getState();
	}

	registerCommands(): Disposable[] {
		return [
			this.host.registerWebviewCommand(InitializeCommand, this.onInitialize, this),
			this.host.registerWebviewCommand(UpdateCommitCommand, this.onUpdateCommit, this),
			this.host.registerWebviewCommand(ReorderCommitsCommand, this.onReorderCommits, this),
			this.host.registerWebviewCommand(CreateCommitCommand, this.onCreateCommit, this),
			this.host.registerWebviewCommand(DeleteCommitCommand, this.onDeleteCommit, this),
			this.host.registerWebviewCommand(SquashCommitsCommand, this.onSquashCommits, this),
			this.host.registerWebviewCommand(SplitCommitCommand, this.onSplitCommit, this),
			this.host.registerWebviewCommand(MoveHunkCommand, this.onMoveHunk, this),
			this.host.registerWebviewCommand(CreateBranchCommand, this.onCreateBranch, this),
			this.host.registerWebviewCommand(UpdateBranchCommand, this.onUpdateBranch, this),
			this.host.registerWebviewCommand(DeleteBranchCommand, this.onDeleteBranch, this),
			this.host.registerWebviewCommand(MoveCommitToBranchCommand, this.onMoveCommitToBranch, this),
			this.host.registerWebviewCommand(SelectCommitCommand, this.onSelectCommit, this),
			this.host.registerWebviewCommand(SelectBranchCommand, this.onSelectBranch, this),
			this.host.registerWebviewCommand(UndoCommand, this.onUndo, this),
			this.host.registerWebviewCommand(RedoCommand, this.onRedo, this),
			this.host.registerWebviewCommand(ExecuteCompositionCommand, this.onExecuteComposition, this),
			this.host.registerWebviewCommand(CancelCompositionCommand, this.onCancelComposition, this),
			this.host.registerWebviewCommand(SaveDraftCommand, this.onSaveDraft, this),
			this.host.registerWebviewCommand(LoadDraftCommand, this.onLoadDraft, this),
			this.host.registerWebviewRequest(RegenerateCommitMessageRequest, this.onRegenerateCommitMessage, this),
			this.host.registerWebviewRequest(GetFileContentRequest, this.onGetFileContent, this),
			this.host.registerWebviewRequest(ValidateCompositionRequest, this.onValidateComposition, this),
			this.host.registerWebviewRequest(PreviewExecutionRequest, this.onPreviewExecution, this),
		];
	}

	private async getState(): Promise<Serialized<State>> {
		if (!this._state) {
			throw new Error('No active composer state');
		}

		return {
			composerState: this._state as Serialized<ComposerState>,
			preferences: {
				showFileTree: true,
				showBranchPanel: true,
				autoSave: false,
				confirmBeforeExecute: true,
			},
			capabilities: {
				canCreateBranches: true,
				canModifyHistory: true,
				hasAI: this.container.ai.enabled,
			},
		};
	}

	private async notifyStateChanged(): Promise<void> {
		const state = await this.getState();
		void this.host.notify(DidChangeStateNotification, { state });
	}

	private async onInitialize(_params: InitializeParams): Promise<void> {
		await this.notifyStateChanged();
	}

	private async onUpdateCommit(params: UpdateCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const updatedCommit = await this.container.commitComposer.updateCommit(
				this._sessionId,
				params.commitId,
				params.changes,
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateCommitNotification, {
				commit: updatedCommit as Serialized<typeof updatedCommit>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onUpdateCommit');
			void window.showErrorMessage(
				`Failed to update commit: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onReorderCommits(params: ReorderCommitsParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.reorderCommits(this._sessionId, params.commitIds);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onReorderCommits');
			void window.showErrorMessage(
				`Failed to reorder commits: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onCreateCommit(params: CreateCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const newCommit = await this.container.commitComposer.createCommit(this._sessionId, params.message, {
				description: params.description,
				afterCommitId: params.afterCommitId,
			});

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateCommitNotification, {
				commit: newCommit as Serialized<typeof newCommit>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onCreateCommit');
			void window.showErrorMessage(
				`Failed to create commit: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onDeleteCommit(params: DeleteCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.deleteCommit(this._sessionId, params.commitId);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onDeleteCommit');
			void window.showErrorMessage(
				`Failed to delete commit: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onSquashCommits(params: SquashCommitsParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			// For now, we'll squash the source into the target
			const message = await window.showInputBox({
				prompt: 'Enter message for squashed commit',
				placeHolder: 'Squashed commit message',
			});

			if (!message) return;

			const squashedCommit = this.container.commitComposer.squashCommits(
				this._sessionId,
				[params.sourceCommitId, params.targetCommitId],
				message,
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateCommitNotification, {
				commit: squashedCommit as Serialized<typeof squashedCommit>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onSquashCommits');
			void window.showErrorMessage(
				`Failed to squash commits: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onSplitCommit(params: SplitCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const commit = this._state?.commits.find(c => c.id === params.commitId);
			if (!commit) {
				throw new Error('Commit not found');
			}

			if (commit.hunks.length < 2) {
				void window.showWarningMessage('Cannot split commit with less than 2 hunks');
				return;
			}

			// Create a new commit with hunks after the split point
			const splitHunks = commit.hunks.slice(params.splitPoint);
			const remainingHunks = commit.hunks.slice(0, params.splitPoint);

			// Update original commit to keep only the first part
			this.container.commitComposer.updateCommit(this._sessionId, params.commitId, {
				hunks: remainingHunks,
			});

			// Create new commit with the split hunks
			const newCommit = this.container.commitComposer.createCommit(
				this._sessionId,
				`${commit.message} (part 2)`,
				{
					description: commit.description,
					afterCommitId: params.commitId,
					hunks: splitHunks,
				},
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateCommitNotification, {
				commit: newCommit as Serialized<typeof newCommit>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onSplitCommit');
			void window.showErrorMessage(
				`Failed to split commit: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onMoveHunk(params: MoveHunkParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.moveHunk(this._sessionId, params.hunkId, params.targetCommitId);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onMoveHunk');
			void window.showErrorMessage(
				`Failed to move hunk: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onCreateBranch(params: CreateBranchParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const newBranch = this.container.commitComposer.createBranch(
				this._sessionId,
				params.name,
				params.baseBranch,
				params.baseCommit,
				{
					description: params.description,
				},
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateBranchNotification, {
				branch: newBranch as Serialized<typeof newBranch>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onCreateBranch');
			void window.showErrorMessage(
				`Failed to create branch: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onUpdateBranch(params: UpdateBranchParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const updatedBranch = this.container.commitComposer.updateBranch(
				this._sessionId,
				params.branchId,
				params.changes,
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);

			void this.host.notify(DidUpdateBranchNotification, {
				branch: updatedBranch as Serialized<typeof updatedBranch>,
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onUpdateBranch');
			void window.showErrorMessage(
				`Failed to update branch: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onDeleteBranch(params: DeleteBranchParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		try {
			const branch = this._state.branches.find(b => b.id === params.branchId);
			if (!branch) {
				throw new Error('Branch not found');
			}

			if (branch.commitIds.length > 0) {
				const result = await window.showWarningMessage(
					`Branch "${branch.name}" contains ${branch.commitIds.length} commit(s). Delete anyway?`,
					{ modal: true },
					'Delete Branch',
					'Cancel',
				);

				if (result !== 'Delete Branch') return;
			}

			// Remove branch from state
			const updatedBranches = this._state.branches.filter(b => b.id !== params.branchId);
			this.container.commitComposer.updateSession(
				this._sessionId,
				{ branches: updatedBranches },
				`Deleted branch: ${branch.name}`,
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onDeleteBranch');
			void window.showErrorMessage(
				`Failed to delete branch: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onMoveCommitToBranch(params: MoveCommitToBranchParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		try {
			const commit = this._state.commits.find(c => c.id === params.commitId);
			const targetBranch = this._state.branches.find(b => b.id === params.targetBranchId);

			if (!commit) {
				throw new Error('Commit not found');
			}

			if (!targetBranch) {
				throw new Error('Target branch not found');
			}

			// Remove commit from all branches
			const updatedBranches = this._state.branches.map(branch => ({
				...branch,
				commitIds: branch.commitIds.filter(id => id !== params.commitId),
			}));

			// Add commit to target branch
			const targetBranchIndex = updatedBranches.findIndex(b => b.id === params.targetBranchId);
			if (targetBranchIndex !== -1) {
				updatedBranches[targetBranchIndex] = {
					...updatedBranches[targetBranchIndex],
					commitIds: [...updatedBranches[targetBranchIndex].commitIds, params.commitId],
				};
			}

			this.container.commitComposer.updateSession(
				this._sessionId,
				{ branches: updatedBranches },
				`Moved commit to branch: ${targetBranch.name}`,
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onMoveCommitToBranch');
			void window.showErrorMessage(
				`Failed to move commit to branch: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onSelectCommit(params: SelectCommitParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		this._state = this.container.commitComposer.updateSession(this._sessionId, {
			selectedCommitId: params.commitId,
		});
		await this.notifyStateChanged();
	}

	private async onSelectBranch(params: SelectBranchParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		this._state = this.container.commitComposer.updateSession(this._sessionId, {
			selectedBranchId: params.branchId,
		});
		await this.notifyStateChanged();
	}

	private async onUndo(): Promise<void> {
		if (!this._sessionId) return;

		try {
			const restoredState = this.container.commitComposer.undoLastAction(this._sessionId);
			if (restoredState) {
				this._state = restoredState;
				await this.notifyStateChanged();
			} else {
				void window.showInformationMessage('Nothing to undo');
			}
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onUndo');
			void window.showErrorMessage(`Failed to undo: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onRedo(): Promise<void> {
		if (!this._sessionId) return;

		try {
			const restoredState = this.container.commitComposer.redoLastAction(this._sessionId);
			if (restoredState) {
				this._state = restoredState;
				await this.notifyStateChanged();
			} else {
				void window.showInformationMessage('Nothing to redo');
			}
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onRedo');
			void window.showErrorMessage(`Failed to redo: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onExecuteComposition(): Promise<void> {
		if (!this._sessionId) return;

		try {
			const result = await this.container.commitComposer.executeComposition(this._sessionId);
			void this.host.notify(DidExecuteNotification, { result });

			if (result.success) {
				void window.showInformationMessage(
					`Successfully created ${result.createdCommits.length} commits and ${result.createdBranches.length} branches!`,
				);
				this.host.close();
			} else {
				void window.showErrorMessage(`Failed to execute composition: ${result.errors.join(', ')}`);
			}
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onExecuteComposition');
			void window.showErrorMessage(
				`Failed to execute composition: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	private async onCancelComposition(): Promise<void> {
		const result = await window.showWarningMessage(
			'Are you sure you want to cancel? All changes will be lost.',
			{ modal: true },
			'Cancel Composition',
			'Keep Editing',
		);

		if (result === 'Cancel Composition') {
			if (this._sessionId) {
				this.container.commitComposer.closeSession(this._sessionId);
			}
			this.host.close();
		}
	}

	private async onSaveDraft(_params: SaveDraftParams): Promise<void> {
		// TODO: Implement save draft
		void window.showInformationMessage('Save draft functionality coming soon!');
	}

	private async onLoadDraft(_params: LoadDraftParams): Promise<void> {
		// TODO: Implement load draft
		void window.showInformationMessage('Load draft functionality coming soon!');
	}

	private async onRegenerateCommitMessage(params: RegenerateCommitMessageParams): Promise<string> {
		// TODO: Implement AI regeneration
		return `Regenerated message for commit ${params.commitId}`;
	}

	private async onGetFileContent(params: GetFileContentParams): Promise<string> {
		// TODO: Implement file content retrieval
		return `File content for ${params.fileUri}`;
	}

	private async onValidateComposition(_params: ValidateCompositionParams): Promise<string[]> {
		if (!this._sessionId) return ['No active session'];

		try {
			const validation = this.container.commitComposer.validateSession(this._sessionId);
			const conflicts = this.container.commitComposer.detectConflicts(this._sessionId);

			const allErrors = [
				...validation.errors.map(e => e.message),
				...conflicts.conflicts.filter(c => c.severity === 'error').map(c => c.message),
			];

			const allWarnings = [
				...validation.warnings.map(w => w.message),
				...conflicts.conflicts.filter(c => c.severity === 'warning').map(c => c.message),
			];

			void this.host.notify(DidValidateNotification, {
				errors: allErrors,
				warnings: allWarnings,
			});

			return allErrors;
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onValidateComposition');
			const errorMessage = error instanceof Error ? error.message : String(error);
			void this.host.notify(DidValidateNotification, {
				errors: [errorMessage],
				warnings: [],
			});
			return [errorMessage];
		}
	}

	private async onPreviewExecution(_params: PreviewExecutionParams): Promise<any> {
		if (!this._sessionId) {
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: ['No active session'],
			};
		}

		try {
			const executionPlan = this.container.commitComposer.getExecutionPlan(this._sessionId);
			const validation = this.container.commitComposer.validateSession(this._sessionId);
			const conflicts = this.container.commitComposer.detectConflicts(this._sessionId);

			return {
				success: validation.isValid && !conflicts.hasConflicts,
				executionPlan,
				validation,
				conflicts,
				estimatedDuration: executionPlan.estimatedDuration,
				risksAssessment: executionPlan.risksAssessment,
			};
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onPreviewExecution');
			return {
				success: false,
				createdCommits: [],
				createdBranches: [],
				errors: [error instanceof Error ? error.message : String(error)],
			};
		}
	}
}
