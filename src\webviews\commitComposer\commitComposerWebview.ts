import type { Disposable, Webview } from 'vscode';
import { window } from 'vscode';
import type { Container } from '../../container';
import type { ComposerState } from '../../models/commitComposer/composerState';
import { Logger } from '../../system/logger';
import type { Serialized } from '../../system/serialize';
import type { IpcMessage } from '../protocol';
import type { WebviewHost, WebviewProvider, WebviewShowingArgs } from '../webviewProvider';
import type { WebviewShowOptions } from '../webviewsController';
import { isSerializedState } from '../webviewsController';
import type {
	State,
	InitializeParams,
	UpdateCommitParams,
	ReorderCommitsParams,
	CreateCommitParams,
	DeleteCommitParams,
	SquashCommitsParams,
	SplitCommitParams,
	MoveHunkParams,
	CreateBranchParams,
	UpdateBranchParams,
	DeleteBranchParams,
	MoveCommitToBranchParams,
	SelectCommitParams,
	SelectBranchParams,
	SaveDraftParams,
	LoadDraftParams,
	RegenerateCommitMessageParams,
	GetFileContentParams,
	ValidateCompositionParams,
	PreviewExecutionParams,
} from './protocol';
import {
	InitializeCommand,
	UpdateCommitCommand,
	ReorderCommitsCommand,
	CreateCommitCommand,
	DeleteCommitCommand,
	SquashCommitsCommand,
	SplitCommitCommand,
	MoveHunkCommand,
	CreateBranchCommand,
	UpdateBranchCommand,
	DeleteBranchCommand,
	MoveCommitToBranchCommand,
	SelectCommitCommand,
	SelectBranchCommand,
	UndoCommand,
	RedoCommand,
	ExecuteCompositionCommand,
	CancelCompositionCommand,
	SaveDraftCommand,
	LoadDraftCommand,
	RegenerateCommitMessageRequest,
	GetFileContentRequest,
	ValidateCompositionRequest,
	PreviewExecutionRequest,
	DidChangeStateNotification,
	DidUpdateCommitNotification,
	DidUpdateBranchNotification,
	DidExecuteNotification,
	DidValidateNotification,
} from './protocol';

export interface CommitComposerWebviewShowingArgs extends WebviewShowingArgs {
	readonly sessionId: string;
	readonly sourceType: 'workingChanges' | 'commitRange';
	readonly sourceRef?: string;
	readonly targetRef?: string;
}

export class CommitComposerWebviewProvider implements WebviewProvider<State, Serialized<State>, CommitComposerWebviewShowingArgs> {
	private readonly _disposable: Disposable;
	private _sessionId: string | undefined;
	private _state: ComposerState | undefined;

	constructor(
		private readonly container: Container,
		private readonly host: WebviewHost<'gitlens.views.commitComposer'>
	) {
		this._disposable = Disposable.from(
			// Listen for composer service events if needed
		);
	}

	dispose(): void {
		this._disposable.dispose();
		if (this._sessionId) {
			this.container.commitComposer.closeSession(this._sessionId);
		}
	}

	getSplitArgs(): Record<string, unknown> {
		return {
			sessionId: this._sessionId,
		};
	}

	async onShowing(
		loading: boolean,
		options?: WebviewShowOptions,
		...args: CommitComposerWebviewShowingArgs[]
	): Promise<boolean> {
		const [{ sessionId, sourceType, sourceRef, targetRef }] = args;
		
		if (loading && this._sessionId === sessionId) {
			return true;
		}

		this._sessionId = sessionId;
		this._state = this.container.commitComposer.getSession(sessionId);

		if (!this._state) {
			void window.showErrorMessage('Commit composer session not found');
			return false;
		}

		return true;
	}

	includeBootstrap(): Promise<Serialized<State>> {
		return this.getState();
	}

	registerCommands(): Disposable[] {
		return [
			this.host.registerWebviewCommand(InitializeCommand, this.onInitialize, this),
			this.host.registerWebviewCommand(UpdateCommitCommand, this.onUpdateCommit, this),
			this.host.registerWebviewCommand(ReorderCommitsCommand, this.onReorderCommits, this),
			this.host.registerWebviewCommand(CreateCommitCommand, this.onCreateCommit, this),
			this.host.registerWebviewCommand(DeleteCommitCommand, this.onDeleteCommit, this),
			this.host.registerWebviewCommand(SquashCommitsCommand, this.onSquashCommits, this),
			this.host.registerWebviewCommand(SplitCommitCommand, this.onSplitCommit, this),
			this.host.registerWebviewCommand(MoveHunkCommand, this.onMoveHunk, this),
			this.host.registerWebviewCommand(CreateBranchCommand, this.onCreateBranch, this),
			this.host.registerWebviewCommand(UpdateBranchCommand, this.onUpdateBranch, this),
			this.host.registerWebviewCommand(DeleteBranchCommand, this.onDeleteBranch, this),
			this.host.registerWebviewCommand(MoveCommitToBranchCommand, this.onMoveCommitToBranch, this),
			this.host.registerWebviewCommand(SelectCommitCommand, this.onSelectCommit, this),
			this.host.registerWebviewCommand(SelectBranchCommand, this.onSelectBranch, this),
			this.host.registerWebviewCommand(UndoCommand, this.onUndo, this),
			this.host.registerWebviewCommand(RedoCommand, this.onRedo, this),
			this.host.registerWebviewCommand(ExecuteCompositionCommand, this.onExecuteComposition, this),
			this.host.registerWebviewCommand(CancelCompositionCommand, this.onCancelComposition, this),
			this.host.registerWebviewCommand(SaveDraftCommand, this.onSaveDraft, this),
			this.host.registerWebviewCommand(LoadDraftCommand, this.onLoadDraft, this),
			this.host.registerWebviewRequest(RegenerateCommitMessageRequest, this.onRegenerateCommitMessage, this),
			this.host.registerWebviewRequest(GetFileContentRequest, this.onGetFileContent, this),
			this.host.registerWebviewRequest(ValidateCompositionRequest, this.onValidateComposition, this),
			this.host.registerWebviewRequest(PreviewExecutionRequest, this.onPreviewExecution, this),
		];
	}

	private async getState(): Promise<Serialized<State>> {
		if (!this._state) {
			throw new Error('No active composer state');
		}

		return {
			composerState: this._state as Serialized<ComposerState>,
			preferences: {
				showFileTree: true,
				showBranchPanel: true,
				autoSave: false,
				confirmBeforeExecute: true,
			},
			capabilities: {
				canCreateBranches: true,
				canModifyHistory: true,
				hasAI: this.container.ai.enabled,
			},
		};
	}

	private async notifyStateChanged(): Promise<void> {
		const state = await this.getState();
		void this.host.notify(DidChangeStateNotification, { state });
	}

	private async onInitialize(_params: InitializeParams): Promise<void> {
		await this.notifyStateChanged();
	}

	private async onUpdateCommit(params: UpdateCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const updatedCommit = await this.container.commitComposer.updateCommit(
				this._sessionId,
				params.commitId,
				params.changes
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);
			
			void this.host.notify(DidUpdateCommitNotification, { 
				commit: updatedCommit as Serialized<typeof updatedCommit>
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onUpdateCommit');
			void window.showErrorMessage(`Failed to update commit: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onReorderCommits(params: ReorderCommitsParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.reorderCommits(this._sessionId, params.commitIds);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onReorderCommits');
			void window.showErrorMessage(`Failed to reorder commits: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onCreateCommit(params: CreateCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			const newCommit = await this.container.commitComposer.createCommit(
				this._sessionId,
				params.message,
				{
					description: params.description,
					afterCommitId: params.afterCommitId,
				}
			);

			this._state = this.container.commitComposer.getSession(this._sessionId);
			
			void this.host.notify(DidUpdateCommitNotification, { 
				commit: newCommit as Serialized<typeof newCommit>
			});
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onCreateCommit');
			void window.showErrorMessage(`Failed to create commit: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onDeleteCommit(params: DeleteCommitParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.deleteCommit(this._sessionId, params.commitId);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onDeleteCommit');
			void window.showErrorMessage(`Failed to delete commit: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onSquashCommits(_params: SquashCommitsParams): Promise<void> {
		// TODO: Implement squash functionality
		void window.showInformationMessage('Squash commits functionality coming soon!');
	}

	private async onSplitCommit(_params: SplitCommitParams): Promise<void> {
		// TODO: Implement split functionality
		void window.showInformationMessage('Split commit functionality coming soon!');
	}

	private async onMoveHunk(params: MoveHunkParams): Promise<void> {
		if (!this._sessionId) return;

		try {
			await this.container.commitComposer.moveHunk(this._sessionId, params.hunkId, params.targetCommitId);
			this._state = this.container.commitComposer.getSession(this._sessionId);
			await this.notifyStateChanged();
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onMoveHunk');
			void window.showErrorMessage(`Failed to move hunk: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onCreateBranch(_params: CreateBranchParams): Promise<void> {
		// TODO: Implement branch creation
		void window.showInformationMessage('Create branch functionality coming soon!');
	}

	private async onUpdateBranch(_params: UpdateBranchParams): Promise<void> {
		// TODO: Implement branch update
		void window.showInformationMessage('Update branch functionality coming soon!');
	}

	private async onDeleteBranch(_params: DeleteBranchParams): Promise<void> {
		// TODO: Implement branch deletion
		void window.showInformationMessage('Delete branch functionality coming soon!');
	}

	private async onMoveCommitToBranch(_params: MoveCommitToBranchParams): Promise<void> {
		// TODO: Implement move commit to branch
		void window.showInformationMessage('Move commit to branch functionality coming soon!');
	}

	private async onSelectCommit(params: SelectCommitParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		this._state = this.container.commitComposer.updateSession(this._sessionId, {
			selectedCommitId: params.commitId,
		});
		await this.notifyStateChanged();
	}

	private async onSelectBranch(params: SelectBranchParams): Promise<void> {
		if (!this._sessionId || !this._state) return;

		this._state = this.container.commitComposer.updateSession(this._sessionId, {
			selectedBranchId: params.branchId,
		});
		await this.notifyStateChanged();
	}

	private async onUndo(): Promise<void> {
		// TODO: Implement undo
		void window.showInformationMessage('Undo functionality coming soon!');
	}

	private async onRedo(): Promise<void> {
		// TODO: Implement redo
		void window.showInformationMessage('Redo functionality coming soon!');
	}

	private async onExecuteComposition(): Promise<void> {
		if (!this._sessionId) return;

		try {
			const result = await this.container.commitComposer.executeComposition(this._sessionId);
			void this.host.notify(DidExecuteNotification, { result });

			if (result.success) {
				void window.showInformationMessage(
					`Successfully created ${result.createdCommits.length} commits and ${result.createdBranches.length} branches!`
				);
				this.host.close();
			} else {
				void window.showErrorMessage(`Failed to execute composition: ${result.errors.join(', ')}`);
			}
		} catch (error) {
			Logger.error(error, 'CommitComposerWebviewProvider', 'onExecuteComposition');
			void window.showErrorMessage(`Failed to execute composition: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async onCancelComposition(): Promise<void> {
		const result = await window.showWarningMessage(
			'Are you sure you want to cancel? All changes will be lost.',
			{ modal: true },
			'Cancel Composition',
			'Keep Editing'
		);

		if (result === 'Cancel Composition') {
			if (this._sessionId) {
				this.container.commitComposer.closeSession(this._sessionId);
			}
			this.host.close();
		}
	}

	private async onSaveDraft(_params: SaveDraftParams): Promise<void> {
		// TODO: Implement save draft
		void window.showInformationMessage('Save draft functionality coming soon!');
	}

	private async onLoadDraft(_params: LoadDraftParams): Promise<void> {
		// TODO: Implement load draft
		void window.showInformationMessage('Load draft functionality coming soon!');
	}

	private async onRegenerateCommitMessage(params: RegenerateCommitMessageParams): Promise<string> {
		// TODO: Implement AI regeneration
		return `Regenerated message for commit ${params.commitId}`;
	}

	private async onGetFileContent(params: GetFileContentParams): Promise<string> {
		// TODO: Implement file content retrieval
		return `File content for ${params.fileUri}`;
	}

	private async onValidateComposition(_params: ValidateCompositionParams): Promise<string[]> {
		if (!this._state) return ['No active session'];

		const errors = this.container.commitComposer.getSession(this._sessionId!)?.validationErrors ?? [];
		void this.host.notify(DidValidateNotification, { errors, warnings: [] });
		return errors;
	}

	private async onPreviewExecution(_params: PreviewExecutionParams): Promise<any> {
		// TODO: Implement execution preview
		return {
			success: true,
			createdCommits: [],
			createdBranches: [],
			errors: [],
		};
	}
}
