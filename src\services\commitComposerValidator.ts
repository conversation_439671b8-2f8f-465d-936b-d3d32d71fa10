import type { ComposerState } from '../models/commitComposer/composerState';
import type { DraftBranch } from '../models/commitComposer/draftBranch';
import type { DraftCommit, DraftHunk } from '../models/commitComposer/draftCommit';

export interface ValidationResult {
	readonly isValid: boolean;
	readonly errors: ValidationError[];
	readonly warnings: ValidationWarning[];
}

export interface ValidationError {
	readonly type: 'error';
	readonly code: string;
	readonly message: string;
	readonly context?: {
		commitId?: string;
		branchId?: string;
		hunkId?: string;
		fileUri?: string;
	};
}

export interface ValidationWarning {
	readonly type: 'warning';
	readonly code: string;
	readonly message: string;
	readonly context?: {
		commitId?: string;
		branchId?: string;
		hunkId?: string;
		fileUri?: string;
	};
}

export interface ConflictDetectionResult {
	readonly hasConflicts: boolean;
	readonly conflicts: CommitConflict[];
}

export interface CommitConflict {
	readonly type: 'hunk-overlap' | 'file-modification' | 'branch-divergence';
	readonly severity: 'error' | 'warning';
	readonly message: string;
	readonly affectedCommits: string[];
	readonly affectedFiles: string[];
	readonly suggestedResolution?: string;
}

/**
 * Advanced validation and conflict detection for commit compositions
 */
export class CommitComposerValidator {
	/**
	 * Perform comprehensive validation of a composer state
	 */
	static validate(state: ComposerState): ValidationResult {
		const errors: ValidationError[] = [];
		const warnings: ValidationWarning[] = [];

		// Basic validation
		this.validateBasicStructure(state, errors, warnings);

		// Commit validation
		this.validateCommits(state, errors, warnings);

		// Branch validation
		this.validateBranches(state, errors, warnings);

		// Hunk validation
		this.validateHunks(state, errors, warnings);

		// Cross-validation
		this.validateCrossReferences(state, errors, warnings);

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
		};
	}

	/**
	 * Detect potential conflicts between commits
	 */
	static detectConflicts(state: ComposerState): ConflictDetectionResult {
		const conflicts: CommitConflict[] = [];

		// Detect hunk overlaps
		this.detectHunkOverlaps(state, conflicts);

		// Detect file modification conflicts
		this.detectFileModificationConflicts(state, conflicts);

		// Detect branch divergence issues
		this.detectBranchDivergence(state, conflicts);

		return {
			hasConflicts: conflicts.some(c => c.severity === 'error'),
			conflicts,
		};
	}

	private static validateBasicStructure(
		state: ComposerState,
		errors: ValidationError[],
		warnings: ValidationWarning[],
	): void {
		if (state.commits.length === 0) {
			errors.push({
				type: 'error',
				code: 'NO_COMMITS',
				message: 'At least one commit is required for composition',
			});
		}

		if (state.branches.length === 0) {
			warnings.push({
				type: 'warning',
				code: 'NO_BRANCHES',
				message: 'No branches defined - commits will be created on the current branch',
			});
		}

		if (state.unassignedHunks.length > 0) {
			warnings.push({
				type: 'warning',
				code: 'UNASSIGNED_HUNKS',
				message: `${state.unassignedHunks.length} hunk(s) are not assigned to any commit`,
			});
		}
	}

	private static validateCommits(
		state: ComposerState,
		errors: ValidationError[],
		warnings: ValidationWarning[],
	): void {
		const commitIds = new Set<string>();

		for (const commit of state.commits) {
			// Check for duplicate IDs
			if (commitIds.has(commit.id)) {
				errors.push({
					type: 'error',
					code: 'DUPLICATE_COMMIT_ID',
					message: `Duplicate commit ID: ${commit.id}`,
					context: { commitId: commit.id },
				});
			}
			commitIds.add(commit.id);

			// Validate commit message
			if (!commit.message.trim()) {
				errors.push({
					type: 'error',
					code: 'EMPTY_COMMIT_MESSAGE',
					message: 'Commit message cannot be empty',
					context: { commitId: commit.id },
				});
			}

			// Check for commits with no changes
			if (commit.hunks.length === 0 && commit.files.length === 0) {
				warnings.push({
					type: 'warning',
					code: 'EMPTY_COMMIT',
					message: `Commit "${commit.message}" has no changes`,
					context: { commitId: commit.id },
				});
			}

			// Validate hunk-file consistency
			const fileUris = new Set(commit.files.map(f => f.uri.toString()));
			for (const hunk of commit.hunks) {
				if (!fileUris.has(hunk.fileUri)) {
					errors.push({
						type: 'error',
						code: 'HUNK_FILE_MISMATCH',
						message: `Hunk references file not included in commit: ${hunk.fileUri}`,
						context: { commitId: commit.id, hunkId: hunk.id, fileUri: hunk.fileUri },
					});
				}
			}
		}
	}

	private static validateBranches(
		state: ComposerState,
		errors: ValidationError[],
		warnings: ValidationWarning[],
	): void {
		const branchNames = new Set<string>();
		const commitIds = new Set(state.commits.map(c => c.id));

		for (const branch of state.branches) {
			// Check for duplicate branch names
			if (branchNames.has(branch.name)) {
				errors.push({
					type: 'error',
					code: 'DUPLICATE_BRANCH_NAME',
					message: `Duplicate branch name: ${branch.name}`,
					context: { branchId: branch.id },
				});
			}
			branchNames.add(branch.name);

			// Validate branch name format
			if (!/^[a-zA-Z0-9/_-]+$/.test(branch.name)) {
				errors.push({
					type: 'error',
					code: 'INVALID_BRANCH_NAME',
					message: `Invalid branch name: ${branch.name}`,
					context: { branchId: branch.id },
				});
			}

			// Check for invalid commit references
			for (const commitId of branch.commitIds) {
				if (!commitIds.has(commitId)) {
					errors.push({
						type: 'error',
						code: 'INVALID_COMMIT_REFERENCE',
						message: `Branch references non-existent commit: ${commitId}`,
						context: { branchId: branch.id, commitId },
					});
				}
			}

			// Warn about empty branches
			if (branch.commitIds.length === 0) {
				warnings.push({
					type: 'warning',
					code: 'EMPTY_BRANCH',
					message: `Branch "${branch.name}" has no commits`,
					context: { branchId: branch.id },
				});
			}
		}
	}

	private static validateHunks(
		state: ComposerState,
		errors: ValidationError[],
		warnings: ValidationWarning[],
	): void {
		const allHunks = [
			...state.commits.flatMap(c => c.hunks),
			...state.unassignedHunks,
		];

		const hunkIds = new Set<string>();

		for (const hunk of allHunks) {
			// Check for duplicate hunk IDs
			if (hunkIds.has(hunk.id)) {
				errors.push({
					type: 'error',
					code: 'DUPLICATE_HUNK_ID',
					message: `Duplicate hunk ID: ${hunk.id}`,
					context: { hunkId: hunk.id },
				});
			}
			hunkIds.add(hunk.id);

			// Validate hunk ranges
			if (hunk.oldStart < 0 || hunk.newStart < 0) {
				errors.push({
					type: 'error',
					code: 'INVALID_HUNK_RANGE',
					message: `Invalid hunk range: ${hunk.id}`,
					context: { hunkId: hunk.id },
				});
			}

			if (hunk.oldLines < 0 || hunk.newLines < 0) {
				errors.push({
					type: 'error',
					code: 'INVALID_HUNK_SIZE',
					message: `Invalid hunk size: ${hunk.id}`,
					context: { hunkId: hunk.id },
				});
			}
		}
	}

	private static validateCrossReferences(
		state: ComposerState,
		errors: ValidationError[],
		warnings: ValidationWarning[],
	): void {
		const commitIds = new Set(state.commits.map(c => c.id));

		// Validate hunk commit references
		for (const hunk of state.unassignedHunks) {
			if (hunk.commitId && !commitIds.has(hunk.commitId)) {
				errors.push({
					type: 'error',
					code: 'INVALID_HUNK_COMMIT_REFERENCE',
					message: `Unassigned hunk references non-existent commit: ${hunk.commitId}`,
					context: { hunkId: hunk.id, commitId: hunk.commitId },
				});
			}
		}
	}

	private static detectHunkOverlaps(state: ComposerState, conflicts: CommitConflict[]): void {
		// Group hunks by file
		const fileHunks = new Map<string, Array<{ hunk: DraftHunk; commitId: string }>>();

		for (const commit of state.commits) {
			for (const hunk of commit.hunks) {
				if (!fileHunks.has(hunk.fileUri)) {
					fileHunks.set(hunk.fileUri, []);
				}
				fileHunks.get(hunk.fileUri)!.push({ hunk, commitId: commit.id });
			}
		}

		// Check for overlaps within each file
		for (const [fileUri, hunks] of fileHunks) {
			const sortedHunks = hunks.sort((a, b) => a.hunk.newStart - b.hunk.newStart);

			for (let i = 0; i < sortedHunks.length - 1; i++) {
				const current = sortedHunks[i];
				const next = sortedHunks[i + 1];

				const currentEnd = current.hunk.newStart + current.hunk.newLines;
				if (currentEnd > next.hunk.newStart) {
					conflicts.push({
						type: 'hunk-overlap',
						severity: 'error',
						message: `Overlapping hunks in ${fileUri}`,
						affectedCommits: [current.commitId, next.commitId],
						affectedFiles: [fileUri],
						suggestedResolution: 'Merge overlapping hunks or move them to the same commit',
					});
				}
			}
		}
	}

	private static detectFileModificationConflicts(state: ComposerState, conflicts: CommitConflict[]): void {
		// Group commits by files they modify
		const fileCommits = new Map<string, string[]>();

		for (const commit of state.commits) {
			for (const file of commit.files) {
				const fileUri = file.uri.toString();
				if (!fileCommits.has(fileUri)) {
					fileCommits.set(fileUri, []);
				}
				fileCommits.get(fileUri)!.push(commit.id);
			}
		}

		// Check for files modified by multiple commits
		for (const [fileUri, commitIds] of fileCommits) {
			if (commitIds.length > 1) {
				conflicts.push({
					type: 'file-modification',
					severity: 'warning',
					message: `File ${fileUri} is modified by multiple commits`,
					affectedCommits: commitIds,
					affectedFiles: [fileUri],
					suggestedResolution: 'Consider consolidating changes to this file into fewer commits',
				});
			}
		}
	}

	private static detectBranchDivergence(state: ComposerState, conflicts: CommitConflict[]): void {
		// Check for commits assigned to multiple branches
		const commitBranches = new Map<string, string[]>();

		for (const branch of state.branches) {
			for (const commitId of branch.commitIds) {
				if (!commitBranches.has(commitId)) {
					commitBranches.set(commitId, []);
				}
				commitBranches.get(commitId)!.push(branch.id);
			}
		}

		for (const [commitId, branchIds] of commitBranches) {
			if (branchIds.length > 1) {
				conflicts.push({
					type: 'branch-divergence',
					severity: 'warning',
					message: `Commit is assigned to multiple branches`,
					affectedCommits: [commitId],
					affectedFiles: [],
					suggestedResolution: 'Assign the commit to only one branch or create separate commits',
				});
			}
		}
	}
}
