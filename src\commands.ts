import './commands/addAuthors';
import './commands/browseRepoAtRevision';
import './commands/closeUnchangedFiles';
import './commands/cloudIntegrations';
import './commands/compareWith';
import './commands/copyCurrentBranch';
import './commands/copyDeepLink';
import './commands/copyMessageToClipboard';
import './commands/copyShaToClipboard';
import './commands/copyRelativePathToClipboard';
import './commands/createPullRequestOnRemote';
import './commands/openDirectoryCompare';
import './commands/diffFolderWithRevision';
import './commands/diffFolderWithRevisionFrom';
import './commands/diffLineWithPrevious';
import './commands/diffLineWithWorking';
import './commands/diffWith';
import './commands/diffWithNext';
import './commands/diffWithPrevious';
import './commands/diffWithRevision';
import './commands/diffWithRevisionFrom';
import './commands/diffWithWorking';
import './commands/externalDiff';
import './commands/explainBranch';
import './commands/explainCommit';
import './commands/explainStash';
import './commands/explainWip';
import './commands/generateChangelog';
import './commands/generateCommitMessage';
import './commands/generateRebase';
import './commands/ghpr/openOrCreateWorktree';
import './commands/gitWizard';
import './commands/interactiveCommitComposer';
import './commands/inviteToLiveShare';
import './commands/inspect';
import './commands/logging';
import './commands/openAssociatedPullRequestOnRemote';
import './commands/openBranchesOnRemote';
import './commands/openBranchOnRemote';
import './commands/openCurrentBranchOnRemote';
import './commands/openChangedFiles';
import './commands/openCommitOnRemote';
import './commands/openComparisonOnRemote';
import './commands/openFileFromRemote';
import './commands/openFileOnRemote';
import './commands/openFileAtRevision';
import './commands/openFileAtRevisionFrom';
import './commands/openOnRemote';
import './commands/openPullRequestOnRemote';
import './commands/openRepoOnRemote';
import './commands/openRevisionFile';
import './commands/openWorkingFile';
import './commands/patches';
import './commands/quickWizard';
import './commands/rebaseEditor';
import './commands/refreshHover';
import './commands/regenerateMarkdownDocument';
import './commands/remoteProviders';
import './commands/repositories';
import './commands/resets';
import './commands/resetViewsLayout';
import './commands/searchCommits';
import './commands/showCommitsInView';
import './commands/showLastQuickPick';
import './commands/openOnlyChangedFiles';
import './commands/showQuickBranchHistory';
import './commands/showQuickCommit';
import './commands/showQuickCommitFile';
import './commands/showQuickFileHistory';
import './commands/showQuickRepoStatus';
import './commands/showQuickStashList';
import './commands/showView';
import './commands/stashApply';
import './commands/stashSave';
import './commands/switchAIModel';
import './commands/switchMode';
import './commands/toggleCodeLens';
import './commands/toggleFileAnnotations';
import './commands/toggleLineBlame';
import './commands/walkthroughs';
