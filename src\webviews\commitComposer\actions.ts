import type { Container } from '../../container';
import type { WebviewViewShowOptions } from '../webviewsController';
import type { CommitComposerWebviewShowingArgs } from './commitComposerWebview';

export async function showCommitComposer(
	args: CommitComposerWebviewShowingArgs,
	showOptions?: WebviewViewShowOptions,
): Promise<void> {
	return Container.instance.views.commitComposer.show(showOptions, args);
}

export async function showInteractiveCommitComposer(
	sessionId: string,
	sourceType: 'workingChanges' | 'commitRange',
	options?: {
		sourceRef?: string;
		targetRef?: string;
		showOptions?: WebviewViewShowOptions;
	}
): Promise<void> {
	return showCommitComposer(
		{
			sessionId,
			sourceType,
			sourceRef: options?.sourceRef,
			targetRef: options?.targetRef,
		},
		options?.showOptions
	);
}
