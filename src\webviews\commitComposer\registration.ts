import type { Serialized } from '../../system/serialize';
import type { WebviewsController } from '../webviewsController';
import type { WebviewViewProxy } from '../webviewsController';
import type { CommitComposerWebviewShowingArgs } from './commitComposerWebview';
import type { State } from './protocol';

export function registerCommitComposerWebview(
	controller: WebviewsController,
): WebviewViewProxy<'gitlens.views.commitComposer', CommitComposerWebviewShowingArgs, Serialized<State>> {
	return controller.registerWebviewView<
		'gitlens.views.commitComposer',
		State,
		Serialized<State>,
		CommitComposerWebviewShowingArgs
	>(
		{
			id: 'gitlens.views.commitComposer',
			fileName: 'commitComposer.html',
			title: 'Interactive Commit Composer',
			contextKeyPrefix: `gitlens:webviewView:commitComposer`,
			trackingFeature: 'commitComposerView',
			type: 'commitComposer',
			plusFeature: true, // This is a premium feature
			webviewHostOptions: {
				retainContextWhenHidden: true,
			},
		},
		async (container, host) => {
			const { CommitComposerWebviewProvider } = await import(
				/* webpackChunkName: "webview-commitComposer" */ './commitComposerWebview'
			);
			return new CommitComposerWebviewProvider(container, host);
		},
	);
}
