<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta http-equiv="Content-Security-Policy" content="#{cspSource}" />
		<meta name="author" content="GitKraken" />
		<link rel="icon" type="image/png" href="#{webroot}/media/gitlens-icon.png" />
		<link href="#{webroot}/commitComposer/commitComposer.css" rel="stylesheet" />
		<title>Interactive Commit Composer</title>
	</head>

	<body class="commit-composer">
		<div class="commit-composer__container">
			<!-- Header -->
			<header class="commit-composer__header">
				<div class="commit-composer__header-content">
					<div class="commit-composer__title">
						<h1>
							<code-icon icon="git-commit"></code-icon>
							Interactive Commit Composer
						</h1>
						<div class="commit-composer__subtitle">
							<span id="source-info">Loading...</span>
						</div>
					</div>
					<div class="commit-composer__actions">
						<button
							id="undo-btn"
							class="commit-composer__action-btn"
							title="Undo (Ctrl+Z)"
							disabled
						>
							<code-icon icon="arrow-left"></code-icon>
						</button>
						<button
							id="redo-btn"
							class="commit-composer__action-btn"
							title="Redo (Ctrl+Y)"
							disabled
						>
							<code-icon icon="arrow-right"></code-icon>
						</button>
						<div class="commit-composer__action-separator"></div>
						<button
							id="validate-btn"
							class="commit-composer__action-btn"
							title="Validate Composition"
						>
							<code-icon icon="check"></code-icon>
						</button>
						<button
							id="preview-btn"
							class="commit-composer__action-btn"
							title="Preview Execution"
						>
							<code-icon icon="eye"></code-icon>
						</button>
						<div class="commit-composer__action-separator"></div>
						<button
							id="save-draft-btn"
							class="commit-composer__action-btn"
							title="Save Draft"
						>
							<code-icon icon="save"></code-icon>
						</button>
						<button
							id="load-draft-btn"
							class="commit-composer__action-btn"
							title="Load Draft"
						>
							<code-icon icon="folder-opened"></code-icon>
						</button>
					</div>
				</div>
			</header>

			<!-- Main Content -->
			<main class="commit-composer__main">
				<!-- Left Panel: Draft Commits -->
				<section class="commit-composer__panel commit-composer__commits-panel">
					<div class="commit-composer__panel-header">
						<h2>
							<code-icon icon="git-commit"></code-icon>
							Draft Commits
							<span id="commit-count" class="commit-composer__count">0</span>
						</h2>
						<div class="commit-composer__panel-actions">
							<button
								id="add-commit-btn"
								class="commit-composer__panel-action"
								title="Add New Commit"
							>
								<code-icon icon="plus"></code-icon>
							</button>
							<button
								id="regenerate-all-btn"
								class="commit-composer__panel-action"
								title="Regenerate All Commit Messages"
							>
								<code-icon icon="refresh"></code-icon>
							</button>
						</div>
					</div>
					<div class="commit-composer__panel-content">
						<div id="commits-list" class="commit-composer__commits-list">
							<!-- Draft commits will be rendered here -->
						</div>
						<div id="commits-empty" class="commit-composer__empty" style="display: none;">
							<code-icon icon="git-commit" size="48"></code-icon>
							<p>No draft commits yet</p>
							<button id="create-first-commit-btn" class="commit-composer__empty-action">
								Create First Commit
							</button>
						</div>
					</div>
				</section>

				<!-- Center Panel: File Changes -->
				<section class="commit-composer__panel commit-composer__changes-panel">
					<div class="commit-composer__panel-header">
						<h2>
							<code-icon icon="diff"></code-icon>
							File Changes
							<span id="changes-count" class="commit-composer__count">0</span>
						</h2>
						<div class="commit-composer__panel-actions">
							<button
								id="toggle-file-tree-btn"
								class="commit-composer__panel-action"
								title="Toggle File Tree View"
							>
								<code-icon icon="list-tree"></code-icon>
							</button>
							<button
								id="expand-all-btn"
								class="commit-composer__panel-action"
								title="Expand All Files"
							>
								<code-icon icon="expand-all"></code-icon>
							</button>
							<button
								id="collapse-all-btn"
								class="commit-composer__panel-action"
								title="Collapse All Files"
							>
								<code-icon icon="collapse-all"></code-icon>
							</button>
						</div>
					</div>
					<div class="commit-composer__panel-content">
						<div id="changes-list" class="commit-composer__changes-list">
							<!-- File changes will be rendered here -->
						</div>
						<div id="changes-empty" class="commit-composer__empty" style="display: none;">
							<code-icon icon="diff" size="48"></code-icon>
							<p>No file changes to display</p>
						</div>
					</div>
				</section>

				<!-- Right Panel: Branch Management -->
				<section class="commit-composer__panel commit-composer__branches-panel">
					<div class="commit-composer__panel-header">
						<h2>
							<code-icon icon="git-branch"></code-icon>
							Branches
							<span id="branches-count" class="commit-composer__count">0</span>
						</h2>
						<div class="commit-composer__panel-actions">
							<button
								id="add-branch-btn"
								class="commit-composer__panel-action"
								title="Create New Branch"
							>
								<code-icon icon="plus"></code-icon>
							</button>
							<button
								id="toggle-branch-panel-btn"
								class="commit-composer__panel-action"
								title="Toggle Branch Panel"
							>
								<code-icon icon="sidebar-right"></code-icon>
							</button>
						</div>
					</div>
					<div class="commit-composer__panel-content">
						<div id="branches-list" class="commit-composer__branches-list">
							<!-- Draft branches will be rendered here -->
						</div>
						<div id="branches-empty" class="commit-composer__empty" style="display: none;">
							<code-icon icon="git-branch" size="48"></code-icon>
							<p>Using default branch</p>
							<button id="create-first-branch-btn" class="commit-composer__empty-action">
								Create New Branch
							</button>
						</div>
					</div>
				</section>
			</main>

			<!-- Footer -->
			<footer class="commit-composer__footer">
				<div class="commit-composer__footer-content">
					<div class="commit-composer__status">
						<div id="validation-status" class="commit-composer__validation">
							<code-icon icon="check" class="commit-composer__validation-icon"></code-icon>
							<span id="validation-text">Ready to compose</span>
						</div>
						<div id="changes-summary" class="commit-composer__summary">
							<span id="summary-text">No changes</span>
						</div>
					</div>
					<div class="commit-composer__footer-actions">
						<button
							id="cancel-btn"
							class="commit-composer__footer-btn commit-composer__footer-btn--secondary"
						>
							Cancel
						</button>
						<button
							id="execute-btn"
							class="commit-composer__footer-btn commit-composer__footer-btn--primary"
							disabled
						>
							<code-icon icon="rocket"></code-icon>
							Execute Composition
						</button>
					</div>
				</div>
			</footer>
		</div>

		<!-- Loading Overlay -->
		<div id="loading-overlay" class="commit-composer__loading" style="display: none;">
			<div class="commit-composer__loading-content">
				<div class="commit-composer__spinner"></div>
				<p id="loading-text">Loading...</p>
			</div>
		</div>

		<!-- Context Menus and Modals will be added dynamically -->
		<div id="context-menus"></div>
		<div id="modals"></div>

		<script type="text/javascript" nonce="#{nonce}">
			window.bootstrap = #{bootstrap};
		</script>
		<script type="text/javascript" src="#{webroot}/commitComposer/commitComposer.js"></script>
	</body>
</html>
