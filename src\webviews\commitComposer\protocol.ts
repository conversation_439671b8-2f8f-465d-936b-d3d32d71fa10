import type { Serialized } from '../../system/serialize';
import type { WebviewState } from '../protocol';
import { IpcCommand, IpcNotification, IpcRequest } from '../protocol';
import type { ComposerState, ComposerExecutionResult } from '../../models/commitComposer/composerState';
import type { DraftCommit, DraftCommitChange } from '../../models/commitComposer/draftCommit';
import type { DraftBranch, DraftBranchChange } from '../../models/commitComposer/draftBranch';

export const scope = 'commitComposer';

export interface State extends WebviewState {
	readonly composerState: ComposerState;
	readonly preferences: {
		readonly showFileTree: boolean;
		readonly showBranchPanel: boolean;
		readonly autoSave: boolean;
		readonly confirmBeforeExecute: boolean;
	};
	readonly capabilities: {
		readonly canCreateBranches: boolean;
		readonly canModifyHistory: boolean;
		readonly hasAI: boolean;
	};
}

// COMMANDS

export interface InitializeParams {
	readonly sourceType: 'workingChanges' | 'commitRange';
	readonly sourceRef?: string;
	readonly targetRef?: string;
}
export const InitializeCommand = new IpcCommand<InitializeParams>(scope, 'initialize');

export interface UpdateCommitParams {
	readonly commitId: string;
	readonly changes: Partial<DraftCommit>;
}
export const UpdateCommitCommand = new IpcCommand<UpdateCommitParams>(scope, 'commit/update');

export interface ReorderCommitsParams {
	readonly commitIds: string[];
}
export const ReorderCommitsCommand = new IpcCommand<ReorderCommitsParams>(scope, 'commits/reorder');

export interface CreateCommitParams {
	readonly message: string;
	readonly description?: string;
	readonly afterCommitId?: string;
}
export const CreateCommitCommand = new IpcCommand<CreateCommitParams>(scope, 'commit/create');

export interface DeleteCommitParams {
	readonly commitId: string;
}
export const DeleteCommitCommand = new IpcCommand<DeleteCommitParams>(scope, 'commit/delete');

export interface SquashCommitsParams {
	readonly sourceCommitId: string;
	readonly targetCommitId: string;
}
export const SquashCommitsCommand = new IpcCommand<SquashCommitsParams>(scope, 'commits/squash');

export interface SplitCommitParams {
	readonly commitId: string;
	readonly splitPoint: number; // Index where to split
}
export const SplitCommitCommand = new IpcCommand<SplitCommitParams>(scope, 'commit/split');

export interface MoveHunkParams {
	readonly hunkId: string;
	readonly targetCommitId: string;
}
export const MoveHunkCommand = new IpcCommand<MoveHunkParams>(scope, 'hunk/move');

export interface CreateBranchParams {
	readonly name: string;
	readonly baseBranch: string;
	readonly baseCommit: string;
	readonly description?: string;
}
export const CreateBranchCommand = new IpcCommand<CreateBranchParams>(scope, 'branch/create');

export interface UpdateBranchParams {
	readonly branchId: string;
	readonly changes: Partial<DraftBranch>;
}
export const UpdateBranchCommand = new IpcCommand<UpdateBranchParams>(scope, 'branch/update');

export interface DeleteBranchParams {
	readonly branchId: string;
}
export const DeleteBranchCommand = new IpcCommand<DeleteBranchParams>(scope, 'branch/delete');

export interface MoveCommitToBranchParams {
	readonly commitId: string;
	readonly targetBranchId: string;
}
export const MoveCommitToBranchCommand = new IpcCommand<MoveCommitToBranchParams>(scope, 'commit/moveToBranch');

export interface SelectCommitParams {
	readonly commitId?: string;
}
export const SelectCommitCommand = new IpcCommand<SelectCommitParams>(scope, 'commit/select');

export interface SelectBranchParams {
	readonly branchId?: string;
}
export const SelectBranchCommand = new IpcCommand<SelectBranchParams>(scope, 'branch/select');

export const UndoCommand = new IpcCommand(scope, 'undo');
export const RedoCommand = new IpcCommand(scope, 'redo');

export const ExecuteCompositionCommand = new IpcCommand(scope, 'execute');
export const CancelCompositionCommand = new IpcCommand(scope, 'cancel');

export interface SaveDraftParams {
	readonly name?: string;
}
export const SaveDraftCommand = new IpcCommand<SaveDraftParams>(scope, 'draft/save');

export interface LoadDraftParams {
	readonly draftId: string;
}
export const LoadDraftCommand = new IpcCommand<LoadDraftParams>(scope, 'draft/load');

// REQUESTS

export interface RegenerateCommitMessageParams {
	readonly commitId: string;
	readonly context?: string;
}
export const RegenerateCommitMessageRequest = new IpcRequest<RegenerateCommitMessageParams, string>(
	scope,
	'commit/regenerateMessage'
);

export interface GetFileContentParams {
	readonly fileUri: string;
	readonly commitId?: string;
}
export const GetFileContentRequest = new IpcRequest<GetFileContentParams, string>(scope, 'file/getContent');

export interface ValidateCompositionParams {}
export const ValidateCompositionRequest = new IpcRequest<ValidateCompositionParams, string[]>(
	scope,
	'composition/validate'
);

export interface PreviewExecutionParams {}
export const PreviewExecutionRequest = new IpcRequest<PreviewExecutionParams, ComposerExecutionResult>(
	scope,
	'execution/preview'
);

// NOTIFICATIONS

export interface DidChangeStateParams {
	readonly state: Serialized<State>;
}
export const DidChangeStateNotification = new IpcNotification<DidChangeStateParams>(scope, 'state/didChange');

export interface DidUpdateCommitParams {
	readonly commit: Serialized<DraftCommit>;
}
export const DidUpdateCommitNotification = new IpcNotification<DidUpdateCommitParams>(scope, 'commit/didUpdate');

export interface DidUpdateBranchParams {
	readonly branch: Serialized<DraftBranch>;
}
export const DidUpdateBranchNotification = new IpcNotification<DidUpdateBranchParams>(scope, 'branch/didUpdate');

export interface DidExecuteParams {
	readonly result: ComposerExecutionResult;
}
export const DidExecuteNotification = new IpcNotification<DidExecuteParams>(scope, 'execution/didExecute');

export interface DidValidateParams {
	readonly errors: string[];
	readonly warnings: string[];
}
export const DidValidateNotification = new IpcNotification<DidValidateParams>(scope, 'composition/didValidate');

// UTILITY TYPES

export interface FileTreeNode {
	readonly uri: string;
	readonly name: string;
	readonly type: 'file' | 'directory';
	readonly children?: FileTreeNode[];
	readonly hasChanges: boolean;
	readonly commitIds: string[];
}

export interface CommitPreview {
	readonly id: string;
	readonly message: string;
	readonly description?: string;
	readonly fileCount: number;
	readonly insertions: number;
	readonly deletions: number;
	readonly branchName?: string;
}

export interface BranchPreview {
	readonly id: string;
	readonly name: string;
	readonly commitCount: number;
	readonly baseBranch: string;
	readonly commits: CommitPreview[];
}
